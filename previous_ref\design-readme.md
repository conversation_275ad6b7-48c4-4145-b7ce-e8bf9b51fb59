npx repomix --remote https://github.com/Phoenix-Sun/small-forest-life -o small-forest-life-packed-core.txt --ignore "public/**,docs/visual-guide.html,**/*.css.d.ts,src/common/components/CloudinaryTest.tsx,src/common/components/SupabaseConnectionTest.tsx,**/*.stories.tsx,**/*.svg"

npm run format
npm run lint:fix


# 【AI 開發指令】小森活 (Small Forest Life) 預購系統 - v1.2 完整開發規格

## 你的角色
你是一位經驗豐富的 React 前端開發者，擔任「小森活 (Small Forest Life)」預購系統的主要開發者。你需完全理解並遵循本規格，使用指定技術棧（Vite, React, MUI v5, LIFF, Supabase, Cloudinary, Jest, ESLint）實現高品質、模組化、可測試的程式碼。所有產出需嚴格遵守本指南的技術棧、架構模式、資料結構和規範。

## 1. 專案概述
基於 LINE 的預購應用，透過 LIFF 提供用戶認證與前端介面。核心功能包括：用戶瀏覽商品、提交訂單（可應用自動數量折扣）；管理員 (Admin/Manager) 管理專案、商品、折扣規則、訂單（包含手動調整折扣）。

## 2. 技術堆疊
- 前端: Vite + React + TypeScript
- UI 庫: Material UI (MUI) v7 + Ant Design v5
- 認證: LINE LIFF SDK (liffId: 2007154933-Jd5gBvvv)
- 後端 (BaaS): Supabase (透過 supabase-js, Functions/RPC)
- 圖片儲存: Cloudinary
- 部署: Vercel
- 測試: Jest/Vitest (待完善)
- 程式碼規範: ESLint + Prettier (React/TypeScript 標準)

## 3. 資料庫結構 (Supabase Tables)

### 通用規則
- 主鍵 id: 均為 uuid，預設 gen_random_uuid()
- 顯示 ID (*_id_display / order_number):
  - 格式: YYYYMMDD-NNNN (日期+4位流水號)
  - 必須由後端 (Supabase Function 或 RPC) 生成，以確保唯一性和格式一致性

### 表結構詳解

#### projects (專案資料)
- id: uuid (主鍵，預設 gen_random_uuid()) - 專案唯一識別碼
- project_id_display: text (唯一, 非空) - 專案 ID (外部顯示用, 例: *********-0001)
- name: text (非空) - 專案名稱
- description: text (可空) - 專案描述
- project_status: text (非空, 預設 'Active') - 專案狀態，可能的值：
  - Active: 進行中，可以預購
    - 初始狀態
    - 用戶可以瀏覽商品並下單
    - 管理員可以編輯所有內容
  - ordering_ended: 結束預購，等待到貨
    - 當前時間超過 deadline 時自動更新
    - 用戶不可再下新訂單
    - 管理員可以查看訂單統計
  - arrived: 已到貨，可以取貨
    - 當前時間超過 arrival_date 時自動更新
    - 系統自動發送到貨通知
    - 用戶可以查看取貨資訊
  - completed: 已結束
    - 需要管理員手動設定
    - 所有訂單都已完成取貨
    - 用於歷史記錄查詢
- default_discount_id: uuid (可空, 外鍵 -> discounts.id) - 預設折扣方案
- owner_id: text (非空, 外鍵 -> users.id) - 專案擁有者
- highlights: jsonb (非空, 預設 '[]') - 專案亮點，格式：[{"icon": "emoji", "text": "說明"}]，最多3個
  - 資料格式範例：
    ```json
    [
      {"icon": "✅", "text": "品質保證"},
      {"icon": "🏡", "text": "在地小農"},
      {"icon": "🚚", "text": "安心配送"}
    ]
    ```
  - 資料驗證規則：
    1. 必須是有效的 JSON 陣列
    2. 每個項目必須包含 icon 和 text 欄位
    3. icon 必須是單一 emoji 字元
    4. text 必須是非空字串
    5. 陣列長度不得超過 3 個項目
  - 常用 emoji 建議：
    - ✅ 品質保證
    - 🏡 在地小農
    - 🚚 安心配送
    - 🌱 有機認證
    - 📦 新鮮配送
    - 🥖 當日現烤
    - ✨ 無添加
    - 🍪 手工製作
    - 🎁 精美包裝
- deadline: timestamp with time zone (可空) - 預購截止時間
- arrival_date: timestamp with time zone (可空) - 預計到貨時間
- images: text[] (可空, 預設 '{}') - 專案圖片 URL 陣列
- created_at: timestamp with time zone (預設 now()) - 建立時間
- updated_at: timestamp with time zone (預設 now()) - 更新時間

### 專案狀態自動更新機制
系統會根據 deadline 和 arrival_date 自動更新專案狀態：
1. 當前時間超過 deadline 時，狀態更新為 ordering_ended
2. 當前時間超過 arrival_date 時，狀態更新為 arrived
3. 管理員可以手動將狀態設為 completed

詳細的觸發器實作和狀態轉換邏輯請參考：[資料庫觸發器與自動化機制說明文檔](database-triggers.md)

### 專案亮點前端實作指南
1. 使用原生 emoji 顯示，不使用 SVG 或圖片
2. 統一的樣式設定（字體大小、對齊方式等）
3. 確保跨平台顯示一致性
4. 提供易用的亮點編輯介面
5. 即時驗證輸入資料符合規範

#### items (商品資料)
- id: uuid (主鍵，預設 gen_random_uuid()) - 商品唯一識別碼
- item_id_display: text (唯一, 非空) - 商品 ID
- project_id: uuid (非空, 外鍵 -> projects.id) - 所屬專案
- name: text (非空) - 商品名稱
- price: numeric (非空) - 商品價格
- description: text (可空) - 商品描述
- status: text (非空, 預設 'Available') - 商品狀態
- sort_order: integer (可空, 預設 0) - 排序順序
- created_at: timestamp with time zone (預設 now()) - 建立時間
- updated_at: timestamp with time zone (預設 now()) - 更新時間

#### discounts (折扣規則)
- id: uuid (主鍵，預設 gen_random_uuid()) - 折扣唯一識別碼
- name: text (非空) - 折扣名稱
- description: text (可空) - 折扣描述
- quantity_threshold: integer (非空) - 數量門檻，必須 > 0
- discount_per_item: numeric (非空) - 每件折扣金額，必須 >= 0
- applicable_item_ids: uuid[] (可空) - 適用商品清單，NULL 表示適用於所有商品
- active: boolean (非空, 預設 true) - 是否啟用
- start_date: timestamp with time zone (可空) - 開始日期
- end_date: timestamp with time zone (可空) - 結束日期，必須晚於開始日期
- created_at: timestamp with time zone (預設 now()) - 建立時間
- updated_at: timestamp with time zone (預設 now()) - 更新時間

資料庫約束：
1. quantity_threshold_positive: 確保數量門檻為正數
2. discount_per_item_positive: 確保每件折扣金額不為負數
3. valid_date_range: 確保結束日期晚於開始日期，或兩者皆為 NULL

註：折扣規則與專案的關聯改由 discount_project_mappings 表格維護，實現多對多關係。

#### discount_project_mappings (折扣規則與專案的對應關係)
- id: uuid (主鍵，預設 gen_random_uuid()) - 對應關係唯一識別碼
- project_id: uuid (非空, 外鍵 -> projects.id) - 專案 ID
- discount_id: uuid (非空, 外鍵 -> discounts.id) - 折扣規則 ID
- created_at: timestamp with time zone (預設 now()) - 建立時間
- updated_at: timestamp with time zone (預設 now()) - 更新時間

資料庫約束：
1. unique_project_discount_mapping: 確保每個專案和折扣規則的組合是唯一的
2. fk_project_id: 外鍵約束，確保 project_id 存在於 projects 表
3. fk_discount_id: 外鍵約束，確保 discount_id 存在於 discounts 表

#### orders (訂單資料)
- id: uuid (主鍵)
- order_number: text (唯一, 非空)
- user_id: text (非空, 外鍵 -> users.id)
- user_name: text (非空)
- project_id: uuid (非空, 外鍵 -> projects.id)
- project_name: text (非空)
- total_amount: numeric (非空, >= 0)
- discount_amount: numeric (非空, 預設 0, >= 0)
- applied_discount_id: uuid (可空, 外鍵 -> discounts.id)
- manual_discount_amount: numeric (非空, 預設 0, >= 0)
- admin_notes: text (可空)
- manual_discount_applied_by: text (可空, 外鍵 -> users.id)
- manual_discount_applied_at: timestamp with time zone (可空)
- final_amount: numeric (非空, >= 0)
- status: text (非空, Enum: 'Pending', 'Confirmed', 'Cancelled', 'Completed', 預設 'Pending') - 訂單狀態
  - Pending: 待確認
    - 用戶剛提交訂單
    - 等待管理員確認
    - 可以取消訂單
  - Confirmed: 已確認
    - 管理員已確認訂單
    - 不可修改訂單內容
    - 等待取貨
  - Cancelled: 已取消
    - 用戶取消或管理員取消
    - 需要記錄取消原因
    - 不計入銷售統計
  - Completed: 已完成
    - 用戶已完成取貨
    - 計入銷售統計
    - 可以查看歷史記錄
- order_date: timestamp with time zone (預設 now())
- pickup_date: date (可空)
- notes: text (可空)
- created_at: timestamp with time zone (預設 now())

#### order_items (訂單項目資料)
- id: uuid (主鍵)
- order_id: uuid (非空, 外鍵 -> orders.id, on delete cascade)
- item_id: uuid (非空, 外鍵 -> items.id, on delete restrict)
- item_name: text (非空)
- quantity: integer (非空, > 0)
- unit_price: numeric (非空, >= 0)
- subtotal: numeric (非空, >= 0)
- created_at: timestamp with time zone (預設 now())

#### users (用戶資料)
- id: text (主鍵) - 用戶的 LINE User ID
- display_name: text (非空) - LINE 顯示名稱
- community_nickname: text (可空) - 社群暱稱
- picture_url: text (可空) - 頭像圖片網址
- role: text (非空, 預設 'user') - 用戶角色
- created_at: timestamp with time zone (預設 now()) - 建立時間
- last_login_at: timestamp with time zone (可空) - 最後登入時間

### 索引建議
- projects: project_id_display (unique), project_status, default_discount_id
- items: item_id_display (unique), project_id, status
- discounts: active, start_date, end_date, GIN 索引 on applicable_item_ids
- discount_project_mappings: (project_id, discount_id) unique, project_id, discount_id
- orders: order_number (unique), user_id, project_id, status, order_date, applied_discount_id, manual_discount_applied_by
- order_items: order_id, item_id
- users: role

### 3.1 資料庫約束與完整性

#### 通用約束規則
1. 金額相關欄位：所有金額欄位必須 >= 0
2. 數量相關欄位：所有數量欄位必須 > 0
3. 狀態欄位：使用 CHECK 約束限制可用值
4. 時間範圍：結束時間必須晚於開始時間
5. 關聯完整性：使用外鍵約束確保資料一致性

#### 表格特定約束

##### orders 表約束
- positive_total_amount: CHECK (total_amount >= 0)
- positive_discount_amount: CHECK (discount_amount >= 0)
- positive_manual_discount_amount: CHECK (manual_discount_amount >= 0)
- valid_final_amount: CHECK (final_amount = total_amount - discount_amount - manual_discount_amount AND final_amount >= 0)
- valid_order_status: CHECK (status IN ('Pending', 'Confirmed', 'Cancelled', 'Completed'))

##### order_items 表約束
- positive_quantity: CHECK (quantity > 0)
- positive_unit_price: CHECK (unit_price >= 0)
- valid_subtotal: CHECK (subtotal = quantity * unit_price)

##### discounts 表約束
- quantity_threshold_positive: CHECK (quantity_threshold > 0)
- discount_per_item_positive: CHECK (discount_per_item >= 0)
- valid_date_range: CHECK ((start_date IS NULL AND end_date IS NULL) OR (start_date IS NOT NULL AND end_date IS NOT NULL AND start_date < end_date))

##### discount_project_mappings 表約束
- unique_discount_project: UNIQUE (discount_id, project_id)

### 3.2 資料庫函數

#### 折扣計算
```sql
FUNCTION calculate_order_discount(
    p_project_id UUID,
    p_cart_items JSONB
) RETURNS TABLE (
    discount_id UUID,
    discount_amount NUMERIC
)
```
- 功能：計算訂單的最佳折扣方案
- 輸入：
  - p_project_id: 專案 ID
  - p_cart_items: 購物車項目，格式：[{"item_id": UUID, "quantity": INTEGER, "unit_price": NUMERIC}]
- 處理邏輯：
  1. 驗證輸入資料的完整性
  2. 計算每個商品的數量和小計
  3. 查詢專案相關的有效折扣規則
     - 檢查折扣是否啟用（active = true）
     - 驗證日期範圍（start_date, end_date）
  4. 針對每個折扣規則：
     - 計算適用商品的總數量
     - 檢查是否達到數量門檻
     - 計算折扣金額
     - 確保折扣不超過適用商品總額
  5. 選擇提供最大優惠的折扣方案
- 返回：折扣 ID 和折扣金額
- 安全性：使用 SECURITY DEFINER 確保安全執行

#### 訂單創建
```sql
FUNCTION create_order_with_items(
    p_user_id TEXT,
    p_project_id UUID,
    p_cart_items JSONB,
    p_pickup_date DATE DEFAULT NULL,
    p_notes TEXT DEFAULT NULL
) RETURNS UUID
```
- 功能：創建訂單及訂單項目
- 處理流程：
  1. 驗證用戶和專案存在性
  2. 生成訂單編號（使用當前日期和序號）
  3. 計算訂單總金額
  4. 呼叫 calculate_order_discount 計算適用折扣
  5. 計算最終金額
  6. 創建訂單記錄
  7. 批量創建訂單項目記錄
- 資料完整性：
  - 確保所有金額計算正確
  - 使用交易確保原子性
  - 驗證所有必要欄位
- 返回：新建訂單的 ID

#### 手動折扣更新
```sql
FUNCTION update_order_manual_discount(
    p_order_id UUID,
    p_manual_discount_amount NUMERIC,
    p_admin_id TEXT,
    p_admin_notes TEXT DEFAULT NULL
) RETURNS void
```
- 功能：更新訂單的手動折扣
- 處理邏輯：
  1. 驗證輸入：
     - 檢查折扣金額非負
     - 驗證訂單存在
     - 檢查管理員權限
  2. 計算新的最終金額：
     - 取得原始總額和自動折扣
     - 計算並驗證新的最終金額
  3. 更新訂單記錄：
     - 設置手動折扣金額
     - 記錄管理員資訊
     - 更新最終金額
  4. 記錄操作歷史
- 安全性：
  - 只允許管理員/經理執行
  - 記錄所有操作細節
  - 確保金額計算正確

### 3.3 資料庫觸發器

#### order_status_change_trigger
- 觸發條件：訂單狀態變更時
- 功能：
  1. 記錄狀態變更歷史：
     - 保存舊狀態和新狀態
     - 記錄變更時間
     - 記錄操作者資訊
  2. 資料收集：
     - 訂單 ID
     - 狀態變更詳情
     - 管理員備註
  3. 歷史記錄：
     - 使用 order_status_history 表
     - 保存完整的變更軌跡
     - 支援稽核追蹤
- 實作細節：
  - 使用 AFTER UPDATE 觸發器
  - 只在狀態實際變更時觸發
  - 自動記錄操作時間戳
  - 使用 auth.uid() 記錄操作者

### 3.4 資料庫視圖

#### order_discounts_view
```sql
CREATE OR REPLACE VIEW public.order_discounts_view AS
SELECT
    o.id as order_id,
    o.order_number,
    o.total_amount,
    d.name as discount_name,
    o.discount_amount as auto_discount_amount,
    o.manual_discount_amount,
    (COALESCE(o.discount_amount, 0) + COALESCE(o.manual_discount_amount, 0)) as total_discount_amount,
    o.final_amount,
    CASE
        WHEN o.manual_discount_amount > 0 THEN
            jsonb_build_object(
                'amount', o.manual_discount_amount,
                'applied_by', u.display_name,
                'applied_at', o.manual_discount_applied_at
            )
        ELSE NULL
    END as manual_discount_info,
    o.admin_notes
FROM
    public.orders o
    LEFT JOIN public.discounts d ON o.applied_discount_id = d.id
    LEFT JOIN public.users u ON o.manual_discount_applied_by = u.id
```
- 功能：整合訂單折扣資訊的視圖
- 欄位說明：
  - order_id: 訂單唯一識別碼
  - order_number: 訂單顯示編號
  - total_amount: 訂單原始總金額
  - discount_name: 自動折扣規則名稱
  - auto_discount_amount: 自動計算的折扣金額
  - manual_discount_amount: 管理員手動調整的折扣金額
  - total_discount_amount: 總折扣金額（自動+手動）
  - final_amount: 最終應付金額
  - manual_discount_info: 手動折扣詳細資訊（JSON格式）
  - admin_notes: 管理員備註說明
- 使用場景：
  1. 訂單列表顯示
  2. 訂單詳情頁面
  3. 折扣報表生成
  4. 管理後台監控

### 3.5 資料完整性檢查

#### 定期檢查查詢
```sql
-- 檢查訂單金額計算是否正確
SELECT o.id, o.order_number, o.total_amount, o.discount_amount, o.final_amount
FROM orders o
WHERE o.final_amount != (o.total_amount - COALESCE(o.discount_amount, 0) - COALESCE(o.manual_discount_amount, 0))
   OR o.final_amount < 0;

-- 檢查折扣規則有效性
SELECT d.id, d.name, d.quantity_threshold, d.discount_per_item
FROM discounts d
WHERE d.active = true
  AND (d.end_date IS NULL OR d.end_date > NOW())
  AND (
    d.quantity_threshold <= 0
    OR d.discount_per_item < 0
    OR (d.start_date IS NOT NULL AND d.end_date IS NOT NULL AND d.start_date >= d.end_date)
  );

-- 檢查訂單項目計算
SELECT oi.id, oi.order_id, oi.quantity, oi.unit_price, oi.subtotal
FROM order_items oi
WHERE oi.subtotal != oi.quantity * oi.unit_price
   OR oi.quantity <= 0
   OR oi.unit_price < 0;
```

#### 監控建議
1. 效能監控：
   - 追蹤折扣計算函數的執行時間
   - 監控大量訂單處理的效能
   - 檢查索引使用情況

2. 資料完整性監控：
   - 定期執行上述檢查查詢
   - 設置自動警報機制
   - 記錄異常情況

3. 安全性監控：
   - 追蹤手動折扣的使用頻率
   - 監控異常的折扣金額
   - 稽核管理員操作記錄

## 4. 核心功能實現

### 4.1. 後端 Display ID 生成
- 必須創建 Supabase Function 或在 RPC 內部實現
- 接收前綴 (e.g., 'P', 'I', 'D', 'O') 作為參數
- 邏輯：獲取當前日期 (YYYYMMDD)，查詢當天已有的最大流水號，加 1 格式化為 4 位數 (NNNN)，拼接並返回
- 需要處理併發和序號重置

### 4.2. 用戶認證 (Auth)
- LIFF: liff.init({ liffId: "2007154933-Jd5gBvvv" }), liff.isLoggedIn(), liff.login(), liff.getProfile()
- 登入/註冊: 獲取 Profile 後，使用 supabase.from('users').upsert(...) 寫入或更新用戶資料，並更新 last_login_at
- 狀態管理: AuthContext 或 useAuth Hook 提供用戶信息
- 權限控制: 前端根據 role 控制 UI 顯示/操作，後端依賴 RLS 強制執行權限
- 後台登入流程:
  1. 用戶通過 /admin/login 進入後台登入頁面
  2. 使用 LIFF SDK 進行身份驗證
  3. 驗證成功後檢查用戶角色 (admin/manager)
  4. 符合權限則進入後台，否則顯示權限不足提示
  5. 登入狀態持久化，確保後台操作的安全性

### 4.3. 專案與商品 (Project & Item)
- 後端生成: project_id_display 和 item_id_display 在創建時由後端 RPC 或觸發器調用 Display ID 生成函數產生
- 整合式管理介面:
  - 專案資訊區塊:
    - 基本資訊：name, description, status, images
    - 預設折扣選擇：default_discount_id
    - 專案操作：複製專案、批量上傳商品
  - 商品列表區塊:
    - 商品 CRUD 操作
    - 商品狀態管理
    - 商品排序
  - 功能特點:
    - 單頁面整合管理，減少切換
    - 即時預覽專案狀態
    - 商品操作時自動更新專案狀態
    - 支援拖拽排序商品
    - 商品批量上傳整合在專案編輯頁面
- 專案複製功能:
  - 提供「複製專案」按鈕，創建完全相同的新專案
  - 複製內容：專案基本信息、圖片、商品設定
  - 自動生成新的 project_id_display
  - 所有關聯商品獲得新的 item_id_display
  - 可選擇是否同時複製折扣設定
- 前端顯示: 用戶只能看到 Active 專案及 Available 狀態的商品

### 4.4. 折扣 (Discount - 數量階梯)

#### 2025/04/11 更新

- **管理端折扣設定已支援：**
  - 選擇折扣適用的專案（單選）
  - 自動載入該專案的商品清單
  - 預設全部商品適用（`applicable_item_ids = null`）
  - 可手動勾選部分商品，存入 UUID 陣列
  - UI 為多選 checkbox，專案切換時自動刷新商品
  - 儲存時自動處理全選/部分選狀態

- **前端折扣計算已修正：**
  - 折扣金額不得超過「適用商品」的小計
  - 與後端 `calculate_order_discount` RPC 完全一致
  - 避免前後端計算不一致問題

- **資料庫現況**
  - discounts 表無 `project_id` 欄位，專案關聯由 `discount_project_mappings` 維護
  - discounts 表 `applicable_item_ids` 欄位缺少 GIN 索引，**請手動建立**：
    ```sql
    CREATE INDEX idx_discounts_applicable_item_ids ON discounts USING GIN (applicable_item_ids);
    ```
  - 所有 CHECK 約束已完善，確保資料完整性

- **未來待辦事項**
  - 增加折扣與專案的多對多管理 UI（目前僅支援單專案）
  - 折扣 CRUD 頁面支援多專案選擇
  - 折扣計算 RPC 支援多專案折扣自動選擇
  - 增加折扣歷史紀錄與審核功能
  - 增加折扣生效/失效自動通知
  - 增加折扣使用次數統計

---
- Admin CRUD: 管理 discounts 表記錄
- 折扣計算相關函數：

1. calculate_order_discount
   - 功能：計算訂單的最佳折扣方案
   - 參數：
     - p_project_id: UUID - 專案 ID
     - p_cart_items: JSONB - 購物車項目，格式：[{"item_id": UUID, "quantity": INTEGER, "unit_price": NUMERIC}]
   - 返回：
     - discount_id: UUID - 適用的折扣 ID
     - discount_amount: NUMERIC - 折扣金額
   - 邏輯：
     - 查詢專案相關的有效折扣規則
     - 計算每個折扣規則的折扣金額
     - 選擇折扣金額最大的規則
     - 考慮商品適用性和數量門檻

2. create_order_with_items
   - 功能：創建訂單並計算折扣
   - 參數：
     - p_user_id: TEXT - 用戶 ID
     - p_project_id: UUID - 專案 ID
     - p_cart_items: JSONB - 購物車項目
     - p_pickup_date: DATE - 取貨日期（可選）
     - p_notes: TEXT - 訂單備註（可選）
   - 返回：UUID - 新建訂單的 ID
   - 邏輯：
     - 生成訂單編號
     - 計算訂單總金額
     - 調用 calculate_order_discount 計算折扣
     - 創建訂單和訂單項目記錄

3. update_order_manual_discount
   - 功能：更新訂單的手動折扣
   - 參數：
     - p_order_id: UUID - 訂單 ID
     - p_manual_discount_amount: NUMERIC - 手動折扣金額
     - p_admin_id: TEXT - 管理員 ID
     - p_admin_notes: TEXT - 管理員備註（可選）
   - 邏輯：
     - 驗證折扣金額不為負數
     - 更新訂單的手動折扣資訊
     - 重新計算最終金額
     - 記錄管理員操作資訊

4. order_discounts_view
   - 功能：訂單折扣資訊視圖
   - 欄位：
     - order_id: UUID - 訂單 ID
     - order_number: TEXT - 訂單編號
     - total_amount: NUMERIC - 訂單總金額
     - discount_name: TEXT - 折扣名稱
     - auto_discount_amount: NUMERIC - 自動折扣金額
     - manual_discount_amount: NUMERIC - 手動折扣金額
     - total_discount_amount: NUMERIC - 總折扣金額
     - final_amount: NUMERIC - 最終金額
     - manual_discount_info: TEXT - 手動折扣操作資訊
     - admin_notes: TEXT - 管理員備註

### 4.5. 購物車與訂單 (Cart & Order)
- 商品選擇: MUI ButtonGroup 或類似元件
- 購物車: CartContext 管理狀態
- 訂單提交 (create_order_with_items Supabase RPC):
  - 原子性操作
  - 生成訂單編號
  - 計算金額和折扣
  - 插入訂單和訂單項目
- 訂單管理 (Admin/Manager):
  - 列表和詳情查看
  - 狀態更新
  - 手動折扣調整

### 4.6. 圖片管理 (Project)
- 上傳: Cloudinary Upload Widget
- 管理: 預覽、刪除、排序

### 4.7. 用戶資料 (User)
- 用戶:
  - 查看個人資料
  - 編輯社群暱稱（community_nickname）
- Admin:
  - 管理用戶列表（優先顯示社群暱稱）
  - 編輯用戶角色
  - 查看用戶完整資料（LINE 名稱、社群暱稱、電話等）

### 4.8. 統計 (Admin)
- 儀表板: 使用 Supabase View 或聚合查詢
- 基本統計: 訂單數量、銷售額等

## 5. UI 規劃

### 命名顯示規則
- 用戶名稱顯示優先級：
  1. 社群暱稱（若已設定）
  2. LINE 顯示名稱（若社群暱稱未設定）
- 管理後台用戶列表：同時顯示社群暱稱和 LINE 名稱，格式為 "社群暱稱 (LINE: xxx)"
- 訂單相關：記錄兩個名稱，顯示時優先使用社群暱稱

### 實現
使用 MUI v5 元件庫，實現響應式佈局

## 6. 開發規範
- 程式碼風格: ESLint
- 類型系統: TypeScript
- 模組化: 元件化、Hooks
- 狀態管理: Context API
- 後端交互: supabase-js SDK
- 性能優化:
  - 參考 [效能優化指南](#效能優化)
- 錯誤處理:
  - 統一的錯誤處理機制
  - 錯誤日誌記錄
  - 使用者友善的錯誤提示

## 7. Supabase Row Level Security (RLS) 策略
- users: 用戶訪問權限控制
- projects: 專案訪問權限
- items: 商品訪問權限
- discounts: 折扣規則訪問權限
- orders: 訂單訪問權限
- order_items: 訂單項目訪問權限

## 8. 安全性設計
詳細的安全性設計請參考：
- [Row Level Security 設定](/docs/row-level-security.md)
- [資料庫觸發器](/docs/database-triggers.md)

### 7.1 基本安全原則
1. 所有表格必須啟用 RLS
2. 所有管理員操作必須使用統一的權限檢查
3. 公開訪問僅限於活躍專案的必要資訊
4. 定期進行安全性審查

### 7.2 API 安全性
1. 請求限制（Rate Limiting）
   - 每個 IP 每分鐘最多 100 個請求
   - 每個用戶每分鐘最多 50 個請求
2. 輸入驗證
   - 所有用戶輸入必須經過驗證
   - 使用 zod 進行資料驗證
3. 敏感資訊處理
   - 敏感資訊不得記錄到日誌
   - 使用環境變數存儲機密資訊

### 資料庫維護與監控指南

#### 1. 定期維護任務
1. 資料清理：
   - 刪除過期的折扣規則
   - 清理無效的折扣專案映射
   - 歸檔已完成的專案資料

2. 效能優化：
   - 更新資料表統計資訊
   - 重建索引以減少碎片
   - 監控並優化慢查詢

3. 資料備份：
   - 每日完整備份
   - 定期測試資料還原
   - 保留操作日誌

#### 2. 監控重點
1. 系統效能：
   - 資料庫連接數
   - 查詢響應時間
   - 資源使用率

2. 資料完整性：
   - 折扣規則有效性
   - 訂單金額計算準確性
   - 用戶權限設定

3. 安全監控：
   - 異常訪問記錄
   - 權限變更追蹤
   - 系統錯誤日誌

#### 3. 效能優化建議
1. 查詢優化：
   - 使用適當的索引
   - 優化 JOIN 操作
   - 實施查詢快取

2. 資料分區：
   - 按時間分區歷史資料
   - 使用適當的分區策略
   - 定期維護分區

3. 連接池管理：
   - 優化連接池設定
   - 監控連接使用情況
   - 及時釋放閒置連接
