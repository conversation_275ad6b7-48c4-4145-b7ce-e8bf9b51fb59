<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>環境變數除錯</title>
    <style>
        body {
            font-family: monospace;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .debug-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .env-var {
            margin: 10px 0;
            padding: 10px;
            background: #f8f9fa;
            border-left: 4px solid #007bff;
        }
        .error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        .success {
            border-left-color: #28a745;
            background: #d4edda;
        }
    </style>
</head>
<body>
    <h1>環境變數除錯頁面</h1>

    <div class="debug-container">
        <h2>環境變數檢查</h2>
        <div id="env-check"></div>
    </div>

    <div class="debug-container">
        <h2>Supabase 模組載入測試</h2>
        <div id="module-check"></div>
    </div>

    <div class="debug-container">
        <h2>Supabase 客戶端測試</h2>
        <div id="client-check"></div>
    </div>

    <!-- Supabase CDN -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>

    <script type="module">
        // 檢查環境變數
        function checkEnvironmentVariables() {
            const envCheckDiv = document.getElementById('env-check');

            const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
            const supabaseKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

            envCheckDiv.innerHTML = `
                <div class="env-var ${supabaseUrl ? 'success' : 'error'}">
                    <strong>VITE_SUPABASE_URL:</strong> ${supabaseUrl || '❌ 未設定'}
                </div>
                <div class="env-var ${supabaseKey ? 'success' : 'error'}">
                    <strong>VITE_SUPABASE_ANON_KEY:</strong> ${supabaseKey ? '✅ 已設定 (' + supabaseKey.substring(0, 20) + '...)' : '❌ 未設定'}
                </div>
                <div class="env-var">
                    <strong>import.meta.env:</strong><br>
                    <pre>${JSON.stringify(import.meta.env, null, 2)}</pre>
                </div>
            `;
        }

        // 檢查 Supabase 模組載入
        async function checkModuleLoading() {
            const moduleCheckDiv = document.getElementById('module-check');

            try {
                if (typeof window !== 'undefined' && window.supabase) {
                    moduleCheckDiv.innerHTML = `
                        <div class="env-var success">
                            ✅ Supabase CDN 載入成功
                        </div>
                        <div class="env-var">
                            <strong>window.supabase:</strong> ${typeof window.supabase}
                        </div>
                        <div class="env-var">
                            <strong>createClient 函數:</strong> ${typeof window.supabase.createClient}
                        </div>
                    `;
                } else {
                    throw new Error('Supabase CDN 未載入');
                }
            } catch (error) {
                moduleCheckDiv.innerHTML = `
                    <div class="env-var error">
                        ❌ Supabase CDN 載入失敗: ${error.message}
                    </div>
                `;
            }
        }

        // 檢查 Supabase 客戶端
        async function checkSupabaseClient() {
            const clientCheckDiv = document.getElementById('client-check');

            try {
                if (!window.supabase) {
                    throw new Error('Supabase CDN 未載入');
                }

                const supabaseUrl = 'https://wmemafmnulbtqvnkixwf.supabase.co';
                const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6IndtZW1hZm1udWxidHF2bmtpeHdmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM2MDIxMTUsImV4cCI6MjA1OTE3ODExNX0.U7hIcTgm2iyIru9XxRI7lBUAYC-ogW3NRLmR4n2wLTA';

                const supabase = window.supabase.createClient(supabaseUrl, supabaseKey);

                clientCheckDiv.innerHTML = `
                    <div class="env-var success">
                        ✅ Supabase 客戶端創建成功
                    </div>
                    <div class="env-var">
                        <strong>客戶端物件:</strong> ${typeof supabase}
                    </div>
                `;

                // 測試連接
                try {
                    const { error } = await supabase.from('projects').select('id').limit(1);
                    if (error) {
                        clientCheckDiv.innerHTML += `
                            <div class="env-var error">
                                ❌ 資料庫連接測試失敗: ${error.message}
                            </div>
                        `;
                    } else {
                        clientCheckDiv.innerHTML += `
                            <div class="env-var success">
                                ✅ 資料庫連接測試成功
                            </div>
                        `;
                    }
                } catch (dbError) {
                    clientCheckDiv.innerHTML += `
                        <div class="env-var error">
                            ❌ 資料庫連接測試異常: ${dbError.message}
                        </div>
                    `;
                }

            } catch (error) {
                clientCheckDiv.innerHTML = `
                    <div class="env-var error">
                        ❌ Supabase 客戶端創建失敗: ${error.message}
                    </div>
                `;
            }
        }

        // 執行所有檢查
        document.addEventListener('DOMContentLoaded', async () => {
            checkEnvironmentVariables();
            await checkModuleLoading();
            await checkSupabaseClient();
        });
    </script>
</body>
</html>
