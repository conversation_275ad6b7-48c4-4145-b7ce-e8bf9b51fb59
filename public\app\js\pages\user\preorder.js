/**
 * 預購頁面 JavaScript 邏輯
 * 負責專案詳情顯示、商品選擇、購物車管理、訂單提交等功能
 */

import { fetchProjectWithItems, fetchProjectDiscounts, createOrderWithItems } from '../../services/supabaseService.js';
import { userInfo, isLoggedIn } from '../../store/authStore.js';
import { cartItems, addItem, updateItemQuantity, removeItem, clearCart, totalCount, subtotal, getItemQuantity } from '../../store/cartStore.js';
import { calculateBestDiscount, calculateFinalAmount, formatAmount, getNextAvailableDiscount } from '../../utils/discountCalculator.js';
import { formatDateTime, formatDate, toISODateString } from '../../utils/dateUtils.js';
import { $, $$, createElement, setContent, addClass, removeClass, on, ready, show, hide } from '../../utils/domUtils.js';

// 頁面狀態
let currentProject = null;
let currentItems = [];
let currentDiscounts = [];
let isLoading = false;

/**
 * 初始化預購頁面
 */
async function initializePreorderPage() {
  console.log('Initializing preorder page...');

  try {
    // 獲取專案ID
    const projectId = getProjectIdFromUrl();
    if (!projectId) {
      showError('無效的專案ID');
      return;
    }

    // 設置事件監聽器
    setupEventListeners();

    // 設置狀態監聽器
    setupStoreListeners();

    // 載入專案資料
    await loadProjectData(projectId);

    console.log('Preorder page initialized successfully');
  } catch (error) {
    console.error('Error initializing preorder page:', error);
    showError('頁面初始化失敗，請重新整理頁面');
  }
}

/**
 * 從 URL 獲取專案ID
 * @returns {string|null} 專案ID
 */
function getProjectIdFromUrl() {
  const urlParams = new URLSearchParams(window.location.search);
  return urlParams.get('id');
}

/**
 * 設置事件監聽器
 */
function setupEventListeners() {
  // 訂單提交按鈕
  const submitBtn = $('#submitOrderBtn');
  if (submitBtn) {
    on(submitBtn, 'click', handleOrderSubmit);
  }

  // 商品數量控制（事件委託）
  const itemsList = $('#itemsList');
  if (itemsList) {
    on(itemsList, 'click', handleQuantityButtonClick);
    on(itemsList, 'change', handleQuantityInputChange);
  }

  // 購物車項目移除（事件委託）
  const cartItemsList = $('#cartItemsList');
  if (cartItemsList) {
    on(cartItemsList, 'click', handleCartItemRemove);
  }
}

/**
 * 設置狀態監聽器
 */
function setupStoreListeners() {
  // 監聽購物車變化
  cartItems.subscribe((items) => {
    updateOrderSummary();
    updateQuantityInputs();
  });

  // 監聽用戶登入狀態
  userInfo.subscribe((user) => {
    updateUserDependentUI(user);
  });
}

/**
 * 載入專案資料
 * @param {string} projectId - 專案ID
 */
async function loadProjectData(projectId) {
  if (isLoading) return;

  isLoading = true;
  showLoading(true);

  try {
    console.log('Loading project data for ID:', projectId);

    // 並行載入專案資訊、商品和折扣規則
    const [projectData, discounts] = await Promise.all([
      fetchProjectWithItems(projectId),
      fetchProjectDiscounts(projectId)
    ]);

    currentProject = projectData.project;
    currentItems = projectData.items;
    currentDiscounts = discounts;

    console.log('Project data loaded:', {
      project: currentProject.name,
      items: currentItems.length,
      discounts: currentDiscounts.length
    });

    // 渲染頁面內容
    renderProjectDetails();
    renderItems();
    updateOrderSummary();

  } catch (error) {
    console.error('Error loading project data:', error);
    showError('載入專案資料失敗，請稍後再試');
  } finally {
    isLoading = false;
    showLoading(false);
  }
}

/**
 * 渲染專案詳情
 */
function renderProjectDetails() {
  if (!currentProject) return;

  // 專案標題
  const titleElement = $('#projectTitle');
  if (titleElement) {
    setContent(titleElement, currentProject.name);
  }

  // 專案狀態
  const statusElement = $('#projectStatus');
  const deadlineElement = $('#projectDeadline');

  if (statusElement && deadlineElement) {
    const statusInfo = getProjectStatusInfo(currentProject);
    setContent(statusElement, statusInfo.label);
    statusElement.className = `status project-status ${statusInfo.className}`;
    setContent(deadlineElement, statusInfo.deadline);
  }

  // 專案描述
  const descriptionElement = $('#projectDescription');
  if (descriptionElement) {
    setContent(descriptionElement, currentProject.description || '');
  }

  // 專案亮點
  const highlightsContainer = $('#projectHighlights');
  if (highlightsContainer && currentProject.highlights) {
    renderHighlights(highlightsContainer, currentProject.highlights);
  }

  // 專案圖片
  const galleryContainer = $('#projectGallery');
  if (galleryContainer && currentProject.images) {
    renderGallery(galleryContainer, currentProject.images);
  }

  // 根據專案狀態控制功能
  updateProjectStatusUI();
}

/**
 * 獲取專案狀態資訊
 * @param {Object} project - 專案資料
 * @returns {Object} 狀態資訊
 */
function getProjectStatusInfo(project) {
  switch (project.project_status) {
    case 'active':
      return {
        label: '進行中',
        className: 'status-active',
        deadline: project.deadline ? `預購截止：${formatDateTime(project.deadline)}` : ''
      };
    case 'ordering_ended':
      return {
        label: '結束預購',
        className: 'status-ordering_ended',
        deadline: project.arrival_date ? `預計到貨：${formatDateTime(project.arrival_date)}` : ''
      };
    case 'arrived':
      return {
        label: '已到貨',
        className: 'status-arrived',
        deadline: '可開始取貨'
      };
    case 'completed':
      return {
        label: '已結束',
        className: 'status-completed',
        deadline: ''
      };
    default:
      return {
        label: '未知狀態',
        className: 'status-unknown',
        deadline: ''
      };
  }
}

/**
 * 渲染專案亮點
 * @param {Element} container - 容器元素
 * @param {Array} highlights - 亮點陣列
 */
function renderHighlights(container, highlights) {
  container.innerHTML = '';

  if (!highlights || highlights.length === 0) {
    hide(container);
    return;
  }

  show(container, 'flex');

  highlights.slice(0, 3).forEach(highlight => {
    const highlightElement = createElement('div', {
      className: 'highlight-item'
    }, [
      createElement('div', { className: 'highlight-icon' }, highlight.icon),
      createElement('div', { className: 'highlight-text' }, highlight.text)
    ]);

    container.appendChild(highlightElement);
  });
}

/**
 * 渲染圖片畫廊
 * @param {Element} container - 容器元素
 * @param {Array} images - 圖片陣列
 */
function renderGallery(container, images) {
  container.innerHTML = '';

  if (!images || images.length === 0) {
    hide(container);
    return;
  }

  show(container);

  images.forEach(imageUrl => {
    const imgElement = createElement('img', {
      src: imageUrl,
      alt: currentProject.name,
      className: 'gallery-image',
      loading: 'lazy'
    });

    container.appendChild(imgElement);
  });
}

/**
 * 渲染商品列表
 */
function renderItems() {
  const itemsListContainer = $('#itemsList');
  if (!itemsListContainer) return;

  itemsListContainer.innerHTML = '';

  if (currentItems.length === 0) {
    const noItemsMessage = createElement('div', {
      className: 'no-items-message',
      style: 'text-align: center; padding: 2rem; color: #666;'
    }, '此專案暫無可預購商品');

    itemsListContainer.appendChild(noItemsMessage);
    return;
  }

  currentItems.forEach(item => {
    const itemElement = createItemCard(item);
    itemsListContainer.appendChild(itemElement);
  });
}

/**
 * 創建商品卡片
 * @param {Object} item - 商品資料
 * @returns {Element} 商品卡片元素
 */
function createItemCard(item) {
  const currentQuantity = getItemQuantity(item.id);

  const itemElement = createElement('div', {
    className: 'item-card',
    dataset: { itemId: item.id }
  }, [
    createElement('div', { className: 'item-info' }, [
      createElement('h3', { className: 'item-name' }, item.name),
      createElement('p', { className: 'item-description' }, item.description || '')
    ]),

    createElement('div', { className: 'item-price' }, `$${formatAmount(item.price)}`),

    createElement('div', { className: 'quantity-control' }, [
      createElement('button', {
        className: 'quantity-btn decrease-qty',
        'aria-label': '減少數量',
        disabled: currentQuantity <= 0
      }, '-'),

      createElement('input', {
        type: 'number',
        className: 'quantity-input',
        value: currentQuantity.toString(),
        min: '0',
        max: '99',
        'aria-label': '數量',
        dataset: {
          itemId: item.id,
          itemName: item.name,
          itemPrice: item.price.toString()
        }
      }),

      createElement('button', {
        className: 'quantity-btn increase-qty',
        'aria-label': '增加數量'
      }, '+')
    ])
  ]);

  return itemElement;
}

/**
 * 處理數量按鈕點擊
 * @param {Event} event - 點擊事件
 */
function handleQuantityButtonClick(event) {
  const button = event.target.closest('.quantity-btn');
  if (!button) return;

  const quantityControl = button.closest('.quantity-control');
  const input = quantityControl.querySelector('.quantity-input');
  const itemId = input.dataset.itemId;
  const itemName = input.dataset.itemName;
  const itemPrice = parseFloat(input.dataset.itemPrice);

  let currentQuantity = parseInt(input.value) || 0;

  if (button.classList.contains('decrease-qty') && currentQuantity > 0) {
    currentQuantity--;
  } else if (button.classList.contains('increase-qty') && currentQuantity < 99) {
    currentQuantity++;
  }

  input.value = currentQuantity;

  // 更新購物車
  if (currentQuantity > 0) {
    updateItemQuantity(itemId, currentQuantity);
  } else {
    removeItem(itemId);
  }
}

/**
 * 處理數量輸入變更
 * @param {Event} event - 變更事件
 */
function handleQuantityInputChange(event) {
  const input = event.target.closest('.quantity-input');
  if (!input) return;

  const itemId = input.dataset.itemId;
  const itemName = input.dataset.itemName;
  const itemPrice = parseFloat(input.dataset.itemPrice);

  let quantity = parseInt(input.value) || 0;

  // 確保數量在有效範圍內
  if (quantity < 0) quantity = 0;
  if (quantity > 99) quantity = 99;

  input.value = quantity;

  // 更新購物車
  if (quantity > 0) {
    const item = {
      id: itemId,
      name: itemName,
      price: itemPrice,
      projectId: currentProject.id
    };

    if (getItemQuantity(itemId) === 0) {
      addItem(item, quantity);
    } else {
      updateItemQuantity(itemId, quantity);
    }
  } else {
    removeItem(itemId);
  }
}

/**
 * 更新數量輸入框
 */
function updateQuantityInputs() {
  const inputs = $$('.quantity-input');

  inputs.forEach(input => {
    const itemId = input.dataset.itemId;
    const currentQuantity = getItemQuantity(itemId);

    input.value = currentQuantity;

    // 更新按鈕狀態
    const quantityControl = input.closest('.quantity-control');
    const decreaseBtn = quantityControl.querySelector('.decrease-qty');
    const increaseBtn = quantityControl.querySelector('.increase-qty');

    decreaseBtn.disabled = currentQuantity <= 0;
    increaseBtn.disabled = currentQuantity >= 99;
  });
}

/**
 * 更新專案狀態相關的 UI
 */
function updateProjectStatusUI() {
  const itemsSection = $('#itemsSelectionSection');
  const orderSummarySection = $('#orderSummarySection');
  const submitBtn = $('#submitOrderBtn');

  if (currentProject.project_status !== 'active') {
    if (itemsSection) hide(itemsSection);
    if (orderSummarySection) hide(orderSummarySection);
    if (submitBtn) submitBtn.disabled = true;
  } else {
    if (itemsSection) show(itemsSection);
    if (orderSummarySection) show(orderSummarySection);
    // submitBtn 的啟用狀態由購物車內容決定
  }
}

/**
 * 更新用戶相關的 UI
 * @param {Object|null} user - 用戶資訊
 */
function updateUserDependentUI(user) {
  const submitBtn = $('#submitOrderBtn');

  if (submitBtn) {
    if (!user && currentProject?.project_status === 'active') {
      submitBtn.textContent = '請先登入';
      submitBtn.disabled = true;
    } else {
      submitBtn.textContent = '確認送出訂單';
      // 按鈕啟用狀態由購物車內容決定
    }
  }
}

// 當 DOM 載入完成時初始化預購頁面
ready(() => {
  // 檢查是否為預購頁面
  const isPreorderPage = window.location.pathname.includes('preorder.html');

  if (isPreorderPage) {
    initializePreorderPage();
  }
});

/**
 * 更新訂單摘要
 */
function updateOrderSummary() {
  const cartItemsList = $('#cartItemsList');
  const subtotalAmount = $('#subtotalAmount');
  const discountAmountRow = $('#discountAmountRow');
  const discountAmountValue = $('#discountAmountValue');
  const finalTotalAmount = $('#finalTotalAmount');
  const discountInfo = $('#discountInfo');
  const submitOrderBtn = $('#submitOrderBtn');

  const currentCartItems = Object.values(cartItems.get());

  // 計算訂單金額
  const calculation = calculateFinalAmount(currentCartItems, currentDiscounts);

  // 更新購物車列表
  if (cartItemsList) {
    renderCartItems(cartItemsList, currentCartItems);
  }

  // 更新金額顯示
  if (subtotalAmount) {
    setContent(subtotalAmount, `$${formatAmount(calculation.subtotal)}`);
  }

  if (discountAmountValue) {
    setContent(discountAmountValue, `-$${formatAmount(calculation.discountAmount)}`);
  }

  if (finalTotalAmount) {
    setContent(finalTotalAmount, `$${formatAmount(calculation.finalAmount)}`);
  }

  // 更新折扣資訊
  if (discountInfo) {
    updateDiscountInfo(discountInfo, calculation);
  }

  // 顯示/隱藏折扣行
  if (discountAmountRow) {
    if (calculation.discountAmount > 0) {
      show(discountAmountRow, 'flex');
    } else {
      hide(discountAmountRow);
    }
  }

  // 更新提交按鈕狀態
  if (submitOrderBtn) {
    const user = userInfo.get();
    const hasItems = currentCartItems.length > 0;
    const isActive = currentProject?.project_status === 'active';

    submitOrderBtn.disabled = !user || !hasItems || !isActive;
  }
}

/**
 * 渲染購物車項目
 * @param {Element} container - 容器元素
 * @param {Array} items - 購物車項目
 */
function renderCartItems(container, items) {
  container.innerHTML = '';

  if (items.length === 0) {
    const emptyMessage = createElement('p', {
      className: 'cart-empty-message'
    }, '請從上方選擇商品');

    container.appendChild(emptyMessage);
    return;
  }

  items.forEach(item => {
    const cartItemElement = createElement('div', {
      className: 'cart-item',
      dataset: { summaryItemId: item.id }
    }, [
      createElement('div', { className: 'cart-item-info' }, [
        createElement('div', { className: 'cart-item-name' }, item.name),
        createElement('div', { className: 'item-quantity' }, `(x${item.quantity})`)
      ]),

      createElement('div', { className: 'cart-item-actions' }, [
        createElement('div', { className: 'cart-item-price' },
          `$${formatAmount(item.quantity * item.price)}`
        ),
        createElement('button', {
          className: 'btn btn-outline btn-sm remove-item-btn',
          dataset: { itemId: item.id }
        }, '移除')
      ])
    ]);

    container.appendChild(cartItemElement);
  });
}

/**
 * 更新折扣資訊
 * @param {Element} element - 折扣資訊元素
 * @param {Object} calculation - 計算結果
 */
function updateDiscountInfo(element, calculation) {
  if (calculation.discountAmount > 0 && calculation.discountDetails) {
    const discountName = calculation.discountDetails.name;
    const discountPercentage = Math.round(
      (calculation.discountAmount / calculation.subtotal) * 100
    );
    setContent(element, `已套用「${discountName}」折扣`);
  } else if (calculation.nextDiscount) {
    setContent(element, calculation.nextDiscount.message);
  } else {
    setContent(element, '');
  }
}

/**
 * 處理購物車項目移除
 * @param {Event} event - 點擊事件
 */
function handleCartItemRemove(event) {
  const removeBtn = event.target.closest('.remove-item-btn');
  if (!removeBtn) return;

  const itemId = removeBtn.dataset.itemId;
  if (itemId) {
    removeItem(itemId);
  }
}

/**
 * 處理訂單提交
 * @param {Event} event - 點擊事件
 */
async function handleOrderSubmit(event) {
  event.preventDefault();

  const user = userInfo.get();
  if (!user) {
    showError('請先登入');
    return;
  }

  const currentCartItems = Object.values(cartItems.get());
  if (currentCartItems.length === 0) {
    showError('請選擇商品');
    return;
  }

  if (currentProject?.project_status !== 'active') {
    showError('此專案目前無法下單');
    return;
  }

  const submitBtn = $('#submitOrderBtn');
  const remarksInput = $('#remarks');
  const pickupDateInput = $('#pickupDate');

  // 獲取表單資料
  const remarks = remarksInput ? remarksInput.value.trim() : '';
  const pickupDate = pickupDateInput ? pickupDateInput.value : '';

  // 驗證取貨日期
  if (pickupDate && new Date(pickupDate) < new Date()) {
    showError('取貨日期不能是過去的日期');
    return;
  }

  // 禁用提交按鈕
  if (submitBtn) {
    submitBtn.disabled = true;
    submitBtn.textContent = '處理中...';
  }

  try {
    // 準備訂單資料
    const orderData = {
      userId: user.id,
      projectId: currentProject.id,
      remarks: remarks,
      pickupDate: pickupDate,
      cartItems: currentCartItems.map(item => ({
        item_id: item.id,
        quantity: item.quantity,
        unit_price: item.price
      }))
    };

    console.log('Submitting order:', orderData);

    // 提交訂單
    const orderId = await createOrderWithItems(orderData);

    console.log('Order created successfully:', orderId);

    // 顯示成功訊息
    showSuccess('訂單已成功提交！');

    // 清空購物車和表單
    clearCart();
    if (remarksInput) remarksInput.value = '';
    if (pickupDateInput) pickupDateInput.value = '';

    // 可選：跳轉到訂單詳情頁面
    setTimeout(() => {
      window.location.href = `order-history.html`;
    }, 2000);

  } catch (error) {
    console.error('Error submitting order:', error);
    showError('訂單提交失敗，請稍後再試');
  } finally {
    // 恢復提交按鈕
    if (submitBtn) {
      submitBtn.disabled = false;
      submitBtn.textContent = '確認送出訂單';
    }
  }
}

/**
 * 顯示載入狀態
 * @param {boolean} show - 是否顯示載入狀態
 */
function showLoading(show) {
  // 可以在這裡添加載入動畫
  console.log('Loading:', show);
}

/**
 * 顯示錯誤訊息
 * @param {string} message - 錯誤訊息
 */
function showError(message) {
  // 創建錯誤提示
  const errorDiv = createElement('div', {
    className: 'error-message',
    style: `
      position: fixed;
      top: 20px;
      left: 50%;
      transform: translateX(-50%);
      background: #f44336;
      color: white;
      padding: 12px 24px;
      border-radius: 4px;
      z-index: 10000;
      font-size: 14px;
    `
  }, message);

  document.body.appendChild(errorDiv);

  // 5秒後自動移除
  setTimeout(() => {
    if (errorDiv.parentNode) {
      errorDiv.parentNode.removeChild(errorDiv);
    }
  }, 5000);
}

/**
 * 顯示成功訊息
 * @param {string} message - 成功訊息
 */
function showSuccess(message) {
  // 創建成功提示
  const successDiv = createElement('div', {
    className: 'success-message',
    style: `
      position: fixed;
      top: 20px;
      left: 50%;
      transform: translateX(-50%);
      background: #4caf50;
      color: white;
      padding: 12px 24px;
      border-radius: 4px;
      z-index: 10000;
      font-size: 14px;
    `
  }, message);

  document.body.appendChild(successDiv);

  // 5秒後自動移除
  setTimeout(() => {
    if (successDiv.parentNode) {
      successDiv.parentNode.removeChild(successDiv);
    }
  }, 5000);
}

// 導出函數供其他模組使用
export { initializePreorderPage };
