<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>訂單管理測試頁面</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .test-container {
            background: white;
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .test-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 16px;
            color: #333;
        }

        .test-button {
            background-color: #57AC5A;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
            font-size: 14px;
            transition: background-color 0.2s;
        }

        .test-button:hover {
            background-color: #4a9c4d;
        }

        .test-button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }

        .test-result {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 12px;
            margin-top: 12px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }

        .success {
            border-color: #57AC5A;
            background-color: #f0f8f0;
            color: #2d5a2f;
        }

        .error {
            border-color: #e74c3c;
            background-color: #fdf2f2;
            color: #721c24;
        }

        .info {
            border-color: #3498db;
            background-color: #f0f8ff;
            color: #1e3a8a;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-success { background-color: #57AC5A; }
        .status-error { background-color: #e74c3c; }
        .status-pending { background-color: #f39c12; }
    </style>
</head>
<body>
    <h1>小森活訂單管理系統 - 測試頁面</h1>

    <!-- Supabase 連接測試 -->
    <div class="test-container">
        <h2 class="test-title">
            <span class="status-indicator status-pending" id="connection-status"></span>
            Supabase 連接測試
        </h2>
        <button class="test-button" id="test-connection-btn">測試連接</button>
        <div class="test-result" id="connection-result">點擊「測試連接」按鈕開始測試...</div>
    </div>

    <!-- 訂單資料測試 -->
    <div class="test-container">
        <h2 class="test-title">
            <span class="status-indicator status-pending" id="orders-status"></span>
            訂單資料測試
        </h2>
        <button class="test-button" id="test-fetch-orders-btn">載入訂單</button>
        <button class="test-button" id="test-fetch-projects-btn">載入專案</button>
        <div class="test-result" id="orders-result">點擊按鈕開始測試...</div>
    </div>

    <!-- 篩選功能測試 -->
    <div class="test-container">
        <h2 class="test-title">
            <span class="status-indicator status-pending" id="filter-status"></span>
            篩選功能測試
        </h2>
        <button class="test-button" id="test-filter-orders-btn">測試篩選</button>
        <button class="test-button" id="test-pagination-btn">測試分頁</button>
        <div class="test-result" id="filter-result">點擊按鈕開始測試...</div>
    </div>

    <!-- 管理功能測試 -->
    <div class="test-container">
        <h2 class="test-title">
            <span class="status-indicator status-pending" id="management-status"></span>
            管理功能測試
        </h2>
        <button class="test-button" id="test-order-details-btn">測試訂單詳情</button>
        <button class="test-button" id="test-status-update-btn" disabled>測試狀態更新</button>
        <div class="test-result" id="management-result">點擊按鈕開始測試...</div>
    </div>

    <!-- 導航連結 -->
    <div class="test-container">
        <h2 class="test-title">快速導航</h2>
        <a href="management/order-management.html" class="test-button" style="text-decoration: none; display: inline-block;">
            前往訂單管理頁面
        </a>
        <a href="dashboard.html" class="test-button" style="text-decoration: none; display: inline-block;">
            返回管理後台
        </a>
        <a href="../user/index.html" class="test-button" style="text-decoration: none; display: inline-block;">
            前台首頁
        </a>
    </div>

    <!-- Supabase CDN -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>

    <script type="module">
        import {
            checkSupabaseConnection,
            fetchAllOrders,
            fetchAllProjects,
            fetchOrderDetails,
            initializeSupabase,
            authenticateAsAdmin,
            isCurrentUserAdmin
        } from '../js/services/supabaseService.js';

        let testOrderId = null;

        // 測試 Supabase 連接
        async function testConnection() {
            const resultDiv = document.getElementById('connection-result');
            const statusIndicator = document.getElementById('connection-status');

            resultDiv.textContent = '正在測試連接...';
            resultDiv.className = 'test-result info';

            try {
                const result = await checkSupabaseConnection();

                if (result.ok) {
                    resultDiv.textContent = '✅ Supabase 連接成功！\n\n資料庫連接正常，可以進行後續測試。';
                    resultDiv.className = 'test-result success';
                    statusIndicator.className = 'status-indicator status-success';
                } else {
                    throw new Error(result.error?.message || '連接失敗');
                }
            } catch (error) {
                resultDiv.textContent = `❌ 連接失敗：\n\n${error.message}\n\n請檢查：\n1. .env 檔案是否正確設置\n2. Supabase 專案是否正常運行\n3. 網路連接是否正常`;
                resultDiv.className = 'test-result error';
                statusIndicator.className = 'status-indicator status-error';
            }
        }

        // 測試載入訂單
        async function testFetchOrders() {
            const resultDiv = document.getElementById('orders-result');
            const statusIndicator = document.getElementById('orders-status');

            resultDiv.textContent = '正在載入訂單資料...';
            resultDiv.className = 'test-result info';

            try {
                // 確保管理員身份驗證
                if (!isCurrentUserAdmin()) {
                    const authSuccess = await authenticateAsAdmin();
                    if (!authSuccess) {
                        throw new Error('管理員身份驗證失敗');
                    }
                }

                const result = await fetchAllOrders({}, { page: 1, limit: 5 });

                if (result.orders && result.orders.length > 0) {
                    testOrderId = result.orders[0].id; // 保存第一個訂單ID用於後續測試

                    const orderSummary = result.orders.map(order =>
                        `${order.order_number} - ${order.user_name} - ${order.status} - $${order.final_amount}`
                    ).join('\n');

                    resultDiv.textContent = `✅ 成功載入 ${result.orders.length} 筆訂單（共 ${result.totalCount} 筆）：\n\n${orderSummary}`;
                    resultDiv.className = 'test-result success';
                    statusIndicator.className = 'status-indicator status-success';

                    // 啟用狀態更新測試按鈕
                    document.getElementById('test-status-update-btn').disabled = false;
                } else {
                    resultDiv.textContent = '⚠️ 資料庫中沒有訂單資料\n\n這可能是正常的，如果這是新的系統。';
                    resultDiv.className = 'test-result info';
                    statusIndicator.className = 'status-indicator status-success';
                }
            } catch (error) {
                resultDiv.textContent = `❌ 載入訂單失敗：\n\n${error.message}`;
                resultDiv.className = 'test-result error';
                statusIndicator.className = 'status-indicator status-error';
            }
        }

        // 測試載入專案
        async function testFetchProjects() {
            const resultDiv = document.getElementById('orders-result');

            resultDiv.textContent = '正在載入專案資料...';
            resultDiv.className = 'test-result info';

            try {
                const projects = await fetchAllProjects();

                if (projects && projects.length > 0) {
                    const projectSummary = projects.map(project =>
                        `${project.project_id_display} - ${project.name} - ${project.project_status}`
                    ).join('\n');

                    resultDiv.textContent = `✅ 成功載入 ${projects.length} 個專案：\n\n${projectSummary}`;
                    resultDiv.className = 'test-result success';
                } else {
                    resultDiv.textContent = '⚠️ 資料庫中沒有專案資料';
                    resultDiv.className = 'test-result info';
                }
            } catch (error) {
                resultDiv.textContent = `❌ 載入專案失敗：\n\n${error.message}`;
                resultDiv.className = 'test-result error';
            }
        }

        // 測試篩選功能
        async function testFilterOrders() {
            const resultDiv = document.getElementById('filter-result');
            const statusIndicator = document.getElementById('filter-status');

            resultDiv.textContent = '正在測試篩選功能...';
            resultDiv.className = 'test-result info';

            try {
                // 測試狀態篩選
                const pendingOrders = await fetchAllOrders({ status: 'Pending' }, { page: 1, limit: 3 });
                const confirmedOrders = await fetchAllOrders({ status: 'Confirmed' }, { page: 1, limit: 3 });

                resultDiv.textContent = `✅ 篩選功能測試成功：\n\n待處理訂單：${pendingOrders.totalCount} 筆\n已確認訂單：${confirmedOrders.totalCount} 筆`;
                resultDiv.className = 'test-result success';
                statusIndicator.className = 'status-indicator status-success';
            } catch (error) {
                resultDiv.textContent = `❌ 篩選功能測試失敗：\n\n${error.message}`;
                resultDiv.className = 'test-result error';
                statusIndicator.className = 'status-indicator status-error';
            }
        }

        // 測試分頁功能
        async function testPagination() {
            const resultDiv = document.getElementById('filter-result');

            resultDiv.textContent = '正在測試分頁功能...';
            resultDiv.className = 'test-result info';

            try {
                const page1 = await fetchAllOrders({}, { page: 1, limit: 2 });
                const page2 = await fetchAllOrders({}, { page: 2, limit: 2 });

                resultDiv.textContent = `✅ 分頁功能測試成功：\n\n第1頁：${page1.orders.length} 筆訂單\n第2頁：${page2.orders.length} 筆訂單\n總計：${page1.totalCount} 筆訂單`;
                resultDiv.className = 'test-result success';
            } catch (error) {
                resultDiv.textContent = `❌ 分頁功能測試失敗：\n\n${error.message}`;
                resultDiv.className = 'test-result error';
            }
        }

        // 測試訂單詳情
        async function testOrderDetails() {
            const resultDiv = document.getElementById('management-result');
            const statusIndicator = document.getElementById('management-status');

            if (!testOrderId) {
                resultDiv.textContent = '⚠️ 請先執行「載入訂單」測試以獲取訂單ID';
                resultDiv.className = 'test-result info';
                return;
            }

            resultDiv.textContent = '正在載入訂單詳情...';
            resultDiv.className = 'test-result info';

            try {
                const orderDetails = await fetchOrderDetails(testOrderId);

                const itemsList = orderDetails.items.map(item =>
                    `  - ${item.item_name} x${item.quantity} = $${item.subtotal}`
                ).join('\n');

                resultDiv.textContent = `✅ 訂單詳情載入成功：\n\n訂單編號：${orderDetails.order_number}\n客戶：${orderDetails.user_name}\n狀態：${orderDetails.status}\n總金額：$${orderDetails.final_amount}\n\n訂單項目：\n${itemsList}`;
                resultDiv.className = 'test-result success';
                statusIndicator.className = 'status-indicator status-success';
            } catch (error) {
                resultDiv.textContent = `❌ 載入訂單詳情失敗：\n\n${error.message}`;
                resultDiv.className = 'test-result error';
                statusIndicator.className = 'status-indicator status-error';
            }
        }

        // 測試狀態更新（僅模擬，不實際更新）
        async function testStatusUpdate() {
            const resultDiv = document.getElementById('management-result');

            resultDiv.textContent = '⚠️ 狀態更新功能已實現，但在測試環境中不執行實際更新操作\n\n功能包括：\n- 單個訂單狀態更新\n- 批量狀態更新\n- 管理員備註\n- 軟刪除（設為已取消）';
            resultDiv.className = 'test-result info';
        }

        // 設置事件監聽器
        document.addEventListener('DOMContentLoaded', () => {
            // 初始化 Supabase
            initializeSupabase();

            // 綁定按鈕事件
            document.getElementById('test-connection-btn').addEventListener('click', testConnection);
            document.getElementById('test-fetch-orders-btn').addEventListener('click', testFetchOrders);
            document.getElementById('test-fetch-projects-btn').addEventListener('click', testFetchProjects);
            document.getElementById('test-filter-orders-btn').addEventListener('click', testFilterOrders);
            document.getElementById('test-pagination-btn').addEventListener('click', testPagination);
            document.getElementById('test-order-details-btn').addEventListener('click', testOrderDetails);
            document.getElementById('test-status-update-btn').addEventListener('click', testStatusUpdate);

            // 頁面載入時自動測試連接
            setTimeout(testConnection, 1000);
        });
    </script>
</body>
</html>
