<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>小森活預購系統 - 首頁</title>
    <link rel="stylesheet" href="../shared-styles.css"> <!-- Shared Styles -->
    <link rel="stylesheet" href="home.css">       <!-- Home Specific Styles -->
    <link rel="stylesheet" href="nav-icons.css">  <!-- Navigation Icons Styles -->
</head>
<body class="page-home"> <!-- Add page specific class -->

  <!-- 統一的頁眉 -->
  <header class="app-header">
    <a href="index.html" class="logo">
      <img src="forest-life-logo.png" alt="小森活 Logo" class="logo-img" />
      <span class="logo-text">小森活</span>
    </a>
    <div class="nav-right">
      <a href="preorder.html" class="cart-btn" aria-label="購物車">
        <span class="nav-icon cart-icon"></span>
        <div class="cart-badge">3</div>
      </a>
      <a href="profile.html" class="user-control" aria-label="使用者中心"> <!-- Link user control to profile -->
         <div class="user-avatar">
             <!-- <img src="/api/placeholder/36/36" alt="用戶頭像"> -->
             <span>U</span> <!-- Fallback Initial -->
         </div>
      </a>
    </div>
  </header>

  <!-- 首頁特定的搜索欄 -->
  <div class="search-container">
    <input type="text" class="form-input search-input" placeholder="搜尋專案..." aria-label="搜尋專案"/>
    <button class="filter-btn" aria-label="篩選">
      <span class="nav-icon search-icon"></span>
    </button>
  </div>

  <!-- 主要內容區域 -->
  <main class="main-content">
    <div class="container">
        <!-- 專案列表將由 JavaScript 動態載入 -->
    </div>
  </main>

  <!-- 統一的底部導航 -->
  <nav class="bottom-nav">
      <a href="index.html" class="nav-item active">
          <span class="nav-icon home-icon"></span>
          <span class="nav-label">首頁</span>
      </a>
      <a href="preorder.html" class="nav-item">
          <span class="nav-icon cart-icon"></span>
          <span class="nav-label">預購</span>
      </a>
      <a href="order-history.html" class="nav-item">
          <span class="nav-icon order-icon"></span>
          <span class="nav-label">我的訂單</span>
      </a>
  </nav>

  <!-- LIFF SDK -->
  <script charset="utf-8" src="https://static.line-scdn.net/liff/edge/2/sdk.js"></script>

  <!-- Supabase CDN -->
  <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>

  <!-- 主要初始化腳本 -->
  <script type="module" src="../js/main.js"></script>

  <!-- 首頁特定腳本 -->
  <script type="module" src="../js/pages/user/home.js"></script>

  <!-- 暫時性管理員入口 - 開發用，後續需移除 -->
  <div id="devAdminEntry" style="
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 9999;
    background: linear-gradient(45deg, #FF6B6B, #FF8E53);
    color: white;
    padding: 12px;
    border-radius: 50%;
    box-shadow: 0 4px 12px rgba(255, 107, 107, 0.3);
    cursor: pointer;
    transition: all 0.3s ease;
    width: 56px;
    height: 56px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    text-decoration: none;
  "
  onmouseover="this.style.transform='scale(1.1)'"
  onmouseout="this.style.transform='scale(1)'"
  onclick="window.open('../admin/dashboard.html', '_blank')"
  title="管理員入口 (開發用)">
    🔐
  </div>
</body>
</html>