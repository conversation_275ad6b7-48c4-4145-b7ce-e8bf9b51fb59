/**
 * 舊版 TypeScript 折扣計算器 - 已更新為 JavaScript 版本
 * 新版本位於：public/app/js/utils/discountCalculator.js
 *
 * 以下代碼已註解，避免誤導和報錯
 */

/*
// 舊版 TypeScript 代碼 - 已廢棄
import { CartItem } from '../types/cart';
import { Discount } from '../types/discount';

export interface DiscountCalculationResult {
  discountId: string | null;
  discountAmount: number;
  discountDetails: {
    name: string;
    description: string | null;
    applicableQuantity: number;
    thresholdQuantity: number;
    discountPerItem: number;
    totalDiscount: number;
    applicableSubtotal: number;
  } | null;
}
*/

/*
// 以下所有函數已廢棄，新版 JavaScript 實現請參考：
// public/app/js/utils/discountCalculator.js

export const calculateQuantityTierDiscount = (
  cartItems: CartItem[],
  discount: Discount
): DiscountCalculationResult => {
  let applicableQuantity = 0;
  let applicableSubtotal = 0;

  cartItems.forEach(item => {
    if (!discount.applicableItemIds?.length || discount.applicableItemIds.includes(item.itemId)) {
      applicableQuantity += item.quantity;
      applicableSubtotal += item.subtotal;
    }
  });

  if (applicableQuantity >= discount.quantityThreshold) {
    const totalDiscount = Math.min(
      applicableQuantity * discount.discountPerItem,
      applicableSubtotal
    );

    return {
      discountId: discount.id,
      discountAmount: totalDiscount,
      discountDetails: {
        name: discount.name,
        description: discount.description,
        applicableQuantity,
        thresholdQuantity: discount.quantityThreshold,
        discountPerItem: discount.discountPerItem,
        totalDiscount,
        applicableSubtotal,
      },
    };
  }

  return {
    discountId: null,
    discountAmount: 0,
    discountDetails: null,
  };
};

export const calculateBestDiscount = (
  cartItems: CartItem[],
  discounts: Discount[]
): DiscountCalculationResult => {
  if (!cartItems.length || !discounts.length) {
    return {
      discountId: null,
      discountAmount: 0,
      discountDetails: null,
    };
  }

  let bestDiscount: DiscountCalculationResult = {
    discountId: null,
    discountAmount: 0,
    discountDetails: null,
  };

  discounts.forEach(discount => {
    const result = calculateQuantityTierDiscount(cartItems, discount);
    if (result.discountAmount > bestDiscount.discountAmount) {
      bestDiscount = result;
    }
  });

  return bestDiscount;
};

export const formatDiscountDescription = (result: DiscountCalculationResult): string => {
  if (!result.discountDetails) {
    return '未符合任何折扣條件';
  }

  const { name, applicableQuantity, thresholdQuantity, discountPerItem, totalDiscount } =
    result.discountDetails;

  return `${name}: 已購買 ${applicableQuantity} 件商品，達到 ${thresholdQuantity} 件門檻，每件折抵 ${discountPerItem} 元，共折抵 ${totalDiscount} 元`;
};
*/

// 新版 JavaScript 實現請參考：
// public/app/js/utils/discountCalculator.js
