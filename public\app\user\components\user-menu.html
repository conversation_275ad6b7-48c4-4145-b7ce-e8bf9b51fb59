<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用戶選單元件</title>
    <link rel="stylesheet" href="../../shared-styles.css">
    <style>
        /* 组件特定样式 */
        body {
            padding: 2rem;
        }
        
        /* 用戶選單 */
        .user-menu {
            position: relative;
            width: fit-content;
        }
        
        .user-btn {
            display: flex;
            align-items: center;
            padding: var(--space-sm);
            border-radius: var(--radius-full);
            background-color: transparent;
            border: none;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .user-btn:hover {
            background-color: var(--bg-light);
        }
        
        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: var(--radius-full);
            background-color: var(--bg-light);
            overflow: hidden;
            margin-right: var(--space-md);
        }
        
        .user-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .user-name {
            font-weight: 500;
            margin-right: var(--space-sm);
            color: var(--text-dark);
        }
        
        .dropdown-icon {
            width: 16px;
            height: 16px;
            color: var(--text-medium);
        }
        
        .user-dropdown {
            position: absolute;
            top: 100%;
            right: 0;
            background-color: var(--bg-white);
            box-shadow: var(--shadow-lg);
            border-radius: var(--radius-md);
            width: 200px;
            z-index: 100;
            padding: var(--space-sm) 0;
            margin-top: var(--space-sm);
        }
        
        .dropdown-item {
            padding: var(--space-sm) var(--space-md);
            display: flex;
            align-items: center;
            color: var(--text-dark);
            text-decoration: none;
            transition: all 0.2s ease;
        }
        
        .dropdown-item:hover {
            background-color: var(--bg-light);
        }
        
        .dropdown-icon-left {
            width: 18px;
            height: 18px;
            margin-right: var(--space-md);
            color: var(--text-medium);
        }
        
        /* 組件展示區 */
        .component-container {
            max-width: 800px;
            margin: 0 auto;
        }
        
        .component-title {
            font-size: 1.8rem;
            font-weight: 700;
            margin-bottom: 2rem;
            color: #333;
        }
        
        .component-description {
            color: #666;
            margin-bottom: 2rem;
        }
        
        .demo-area {
            border: 1px dashed #ddd;
            padding: 2rem;
            border-radius: 8px;
            background-color: white;
            margin-bottom: 2rem;
        }
        
        .usage-code {
            background-color: #f8f8f8;
            border-radius: 8px;
            padding: 1.5rem;
            font-family: monospace;
            overflow-x: auto;
            margin-bottom: 2rem;
        }
        
        .header-demo {
            display: flex;
            justify-content: flex-end;
            padding: 1rem;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            margin-bottom: 2rem;
        }
        
        /* 不同狀態演示 */
        .states-section {
            margin-bottom: 2rem;
        }
        
        .section-title {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid #eee;
        }
        
        .states-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 1.5rem;
        }
        
        .state-card {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            padding: 1.5rem;
        }
        
        .state-title {
            font-weight: 600;
            margin-bottom: 1rem;
            color: #444;
        }
        
        .state-demo {
            display: flex;
            justify-content: center;
        }
    </style>
</head>
<body>
    <div class="component-container">
        <h1 class="component-title">用戶選單元件</h1>
        
        <p class="component-description">
            用戶選單元件位於頁面頂部右側，提供用戶快速訪問訂單歷史、個人設定和登出功能。
            用戶點擊頭像或名稱時顯示下拉選單。
        </p>
        
        <!-- 標準頭部示範 -->
        <div class="header-demo">
            <div class="user-menu">
                <button class="user-btn">
                    <div class="user-avatar">
                        <img src="/api/placeholder/40/40" alt="用戶頭像">
                    </div>
                    <span class="user-name">王小明 (社群暱稱)</span>
                    <svg class="dropdown-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <polyline points="6 9 12 15 18 9"></polyline>
                    </svg>
                </button>
            </div>
        </div>
        
        <!-- 元件演示區 -->
        <h2 class="section-title">元件演示</h2>
        
        <div class="demo-area">
            <div class="user-menu">
                <button class="user-btn">
                    <div class="user-avatar">
                        <img src="/api/placeholder/40/40" alt="用戶頭像">
                    </div>
                    <span class="user-name">王小明 (社群暱稱)</span>
                    <svg class="dropdown-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <polyline points="6 9 12 15 18 9"></polyline>
                    </svg>
                </button>
                
                <div class="user-dropdown">
                    <a href="../order-history.html" class="dropdown-item">
                        <svg class="dropdown-icon-left" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                            <polyline points="14 2 14 8 20 8"></polyline>
                            <line x1="16" y1="13" x2="8" y2="13"></line>
                            <line x1="16" y1="17" x2="8" y2="17"></line>
                            <polyline points="10 9 9 9 8 9"></polyline>
                        </svg>
                        訂單記錄
                    </a>
                    <a href="#" class="dropdown-item">
                        <svg class="dropdown-icon-left" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                            <circle cx="12" cy="7" r="4"></circle>
                        </svg>
                        個人設定
                    </a>
                    <a href="#" class="dropdown-item">
                        <svg class="dropdown-icon-left" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"></path>
                            <polyline points="16 17 21 12 16 7"></polyline>
                            <line x1="21" y1="12" x2="9" y2="12"></line>
                        </svg>
                        登出
                    </a>
                </div>
            </div>
        </div>
        
        <!-- 使用方法 -->
        <h2 class="section-title">使用方法</h2>
        
        <pre class="usage-code">
&lt;!-- 頁頭中的用戶選單 --&gt;
&lt;div class="user-menu"&gt;
    &lt;button class="user-btn"&gt;
        &lt;div class="user-avatar"&gt;
            &lt;img src="/path/to/avatar.jpg" alt="用戶頭像"&gt;
        &lt;/div&gt;
        &lt;span class="user-name"&gt;用戶名稱&lt;/span&gt;
        &lt;svg class="dropdown-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"&gt;
            &lt;polyline points="6 9 12 15 18 9"&gt;&lt;/polyline&gt;
        &lt;/svg&gt;
    &lt;/button&gt;
    
    &lt;div class="user-dropdown"&gt;
        &lt;a href="order-history.html" class="dropdown-item"&gt;
            &lt;svg class="dropdown-icon-left" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"&gt;
                &lt;path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"&gt;&lt;/path&gt;
                &lt;polyline points="14 2 14 8 20 8"&gt;&lt;/polyline&gt;
                &lt;line x1="16" y1="13" x2="8" y2="13"&gt;&lt;/line&gt;
                &lt;line x1="16" y1="17" x2="8" y2="17"&gt;&lt;/line&gt;
                &lt;polyline points="10 9 9 9 8 9"&gt;&lt;/polyline&gt;
            &lt;/svg&gt;
            訂單記錄
        &lt;/a&gt;
        &lt;a href="#" class="dropdown-item"&gt;
            &lt;svg class="dropdown-icon-left" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"&gt;
                &lt;path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"&gt;&lt;/path&gt;
                &lt;circle cx="12" cy="7" r="4"&gt;&lt;/circle&gt;
            &lt;/svg&gt;
            個人設定
        &lt;/a&gt;
        &lt;a href="#" class="dropdown-item"&gt;
            &lt;svg class="dropdown-icon-left" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"&gt;
                &lt;path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"&gt;&lt;/path&gt;
                &lt;polyline points="16 17 21 12 16 7"&gt;&lt;/polyline&gt;
                &lt;line x1="21" y1="12" x2="9" y2="12"&gt;&lt;/line&gt;
            &lt;/svg&gt;
            登出
        &lt;/a&gt;
    &lt;/div&gt;
&lt;/div&gt;
        </pre>
        
        <!-- 不同狀態展示 -->
        <h2 class="section-title">不同狀態展示</h2>
        
        <div class="states-grid">
            <!-- 預設狀態 -->
            <div class="state-card">
                <h3 class="state-title">預設狀態</h3>
                <div class="state-demo">
                    <div class="user-menu">
                        <button class="user-btn">
                            <div class="user-avatar">
                                <img src="/api/placeholder/40/40" alt="用戶頭像">
                            </div>
                            <span class="user-name">王小明</span>
                            <svg class="dropdown-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <polyline points="6 9 12 15 18 9"></polyline>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- 僅顯示頭像 -->
            <div class="state-card">
                <h3 class="state-title">僅顯示頭像</h3>
                <div class="state-demo">
                    <div class="user-menu">
                        <button class="user-btn">
                            <div class="user-avatar">
                                <img src="/api/placeholder/40/40" alt="用戶頭像">
                            </div>
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- 無頭像 -->
            <div class="state-card">
                <h3 class="state-title">無頭像</h3>
                <div class="state-demo">
                    <div class="user-menu">
                        <button class="user-btn">
                            <span class="user-name">王小明</span>
                            <svg class="dropdown-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <polyline points="6 9 12 15 18 9"></polyline>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- 社群暱稱顯示 -->
            <div class="state-card">
                <h3 class="state-title">社群暱稱顯示</h3>
                <div class="state-demo">
                    <div class="user-menu">
                        <button class="user-btn">
                            <div class="user-avatar">
                                <img src="/api/placeholder/40/40" alt="用戶頭像">
                            </div>
                            <span class="user-name">王小明 (社群暱稱)</span>
                            <svg class="dropdown-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <polyline points="6 9 12 15 18 9"></polyline>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- JavaScript 整合說明 -->
        <h2 class="section-title">JavaScript 整合</h2>
        
        <p>以下是用戶選單切換顯示隱藏的簡單JavaScript代碼:</p>
        
        <pre class="usage-code">
// 用戶選單下拉顯示切換
document.addEventListener('DOMContentLoaded', function() {
    const userBtn = document.querySelector('.user-btn');
    const userDropdown = document.querySelector('.user-dropdown');
    
    // 點擊用戶按鈕切換下拉選單顯示/隱藏
    userBtn.addEventListener('click', function(e) {
        e.stopPropagation();
        userDropdown.style.display = userDropdown.style.display === 'block' ? 'none' : 'block';
    });
    
    // 點擊頁面其他區域關閉下拉選單
    document.addEventListener('click', function() {
        userDropdown.style.display = 'none';
    });
    
    // 防止點擊下拉選單內部時關閉選單
    userDropdown.addEventListener('click', function(e) {
        e.stopPropagation();
    });
});
        </pre>
        
        <!-- 備註說明 -->
        <h2 class="section-title">備註</h2>
        
        <ul class="notes-list">
            <li>用戶名稱顯示優先順序為：社群暱稱 > LINE 顯示名稱</li>
            <li>若使用者已設定社群暱稱，則顯示格式為「社群暱稱 (LINE: xxx)」</li>
            <li>用戶選單在手機版頁面可收合在底部導航中</li>
            <li>需整合 LIFF SDK 獲取用戶資訊</li>
        </ul>
    </div>
</body>
</html>
