/**
 * 訂單歷史頁面 JavaScript 邏輯
 * 負責顯示用戶的訂單歷史記錄
 */

import { fetchUserOrders } from '../../services/supabaseService.js';
import { userInfo, isLoggedIn } from '../../store/authStore.js';
import { formatDateTime, formatDate, getRelativeTime } from '../../utils/dateUtils.js';
import { formatAmount } from '../../utils/discountCalculator.js';
import { $, $$, createElement, setContent, addClass, removeClass, on, ready, show, hide } from '../../utils/domUtils.js';

// 頁面狀態
let orders = [];
let filteredOrders = [];
let isLoading = false;

/**
 * 初始化訂單歷史頁面
 */
async function initializeOrderHistoryPage() {
  console.log('Initializing order history page...');
  
  try {
    // 設置事件監聽器
    setupEventListeners();
    
    // 設置狀態監聽器
    setupStoreListeners();
    
    // 檢查用戶登入狀態
    const user = userInfo.get();
    if (user) {
      await loadUserOrders(user.id);
    } else {
      showLoginRequired();
    }
    
    console.log('Order history page initialized successfully');
  } catch (error) {
    console.error('Error initializing order history page:', error);
    showError('頁面初始化失敗，請重新整理頁面');
  }
}

/**
 * 設置事件監聽器
 */
function setupEventListeners() {
  // 篩選按鈕
  const filterButtons = $$('.filter-btn');
  filterButtons.forEach(button => {
    on(button, 'click', handleFilterClick);
  });

  // 搜尋輸入框
  const searchInput = $('.search-input');
  if (searchInput) {
    on(searchInput, 'input', debounceSearch);
    on(searchInput, 'keypress', (event) => {
      if (event.key === 'Enter') {
        event.preventDefault();
        performSearch();
      }
    });
  }

  // 訂單卡片點擊事件（事件委託）
  const ordersContainer = $('.orders-container');
  if (ordersContainer) {
    on(ordersContainer, 'click', handleOrderCardClick);
  }
}

/**
 * 設置狀態監聽器
 */
function setupStoreListeners() {
  // 監聽用戶登入狀態變化
  userInfo.subscribe((user) => {
    if (user) {
      loadUserOrders(user.id);
    } else {
      showLoginRequired();
    }
  });
}

/**
 * 載入用戶訂單
 * @param {string} userId - 用戶ID
 */
async function loadUserOrders(userId) {
  if (isLoading) return;
  
  isLoading = true;
  showLoading(true);
  
  try {
    console.log('Loading user orders for:', userId);
    orders = await fetchUserOrders(userId);
    filteredOrders = [...orders];
    
    console.log('Orders loaded:', orders.length);
    renderOrders(filteredOrders);
    
  } catch (error) {
    console.error('Error loading user orders:', error);
    showError('載入訂單失敗，請稍後再試');
  } finally {
    isLoading = false;
    showLoading(false);
  }
}

/**
 * 渲染訂單列表
 * @param {Array} ordersToRender - 要渲染的訂單列表
 */
function renderOrders(ordersToRender) {
  const container = $('.orders-container');
  if (!container) return;

  container.innerHTML = '';

  if (ordersToRender.length === 0) {
    const noOrdersDiv = createElement('div', {
      className: 'no-orders-message',
      style: 'text-align: center; padding: 2rem; color: #666;'
    }, '目前沒有訂單記錄');
    
    container.appendChild(noOrdersDiv);
    return;
  }

  // 渲染訂單卡片
  ordersToRender.forEach(order => {
    const orderCard = createOrderCard(order);
    container.appendChild(orderCard);
  });
}

/**
 * 創建訂單卡片
 * @param {Object} order - 訂單資料
 * @returns {Element} 訂單卡片元素
 */
function createOrderCard(order) {
  const statusInfo = getOrderStatusInfo(order.status);
  
  const orderCard = createElement('div', {
    className: 'order-card',
    dataset: { orderId: order.id }
  }, [
    createElement('div', { className: 'order-header' }, [
      createElement('div', { className: 'order-number' }, order.order_number),
      createElement('div', {
        className: `order-status ${statusInfo.className}`
      }, statusInfo.label)
    ]),
    
    createElement('div', { className: 'order-info' }, [
      createElement('div', { className: 'order-project' }, order.project_name),
      createElement('div', { className: 'order-date' }, formatDate(order.created_at)),
      createElement('div', { className: 'order-amount' }, `$${formatAmount(order.final_amount)}`)
    ]),
    
    createElement('div', { className: 'order-footer' }, [
      createElement('div', { className: 'order-time' }, getRelativeTime(order.created_at)),
      createElement('button', {
        className: 'btn btn-outline btn-sm view-details-btn',
        dataset: { orderId: order.id }
      }, '查看詳情')
    ])
  ]);

  return orderCard;
}

/**
 * 獲取訂單狀態資訊
 * @param {string} status - 訂單狀態
 * @returns {Object} 狀態資訊
 */
function getOrderStatusInfo(status) {
  switch (status) {
    case 'Pending':
      return {
        label: '待確認',
        className: 'status-pending'
      };
    case 'Confirmed':
      return {
        label: '已確認',
        className: 'status-confirmed'
      };
    case 'Cancelled':
      return {
        label: '已取消',
        className: 'status-cancelled'
      };
    case 'Completed':
      return {
        label: '已完成',
        className: 'status-completed'
      };
    default:
      return {
        label: '未知狀態',
        className: 'status-unknown'
      };
  }
}

/**
 * 處理篩選按鈕點擊
 * @param {Event} event - 點擊事件
 */
function handleFilterClick(event) {
  const button = event.target.closest('.filter-btn');
  if (!button) return;

  const filterType = button.dataset.filter;
  
  // 更新按鈕狀態
  $$('.filter-btn').forEach(btn => removeClass(btn, 'active'));
  addClass(button, 'active');
  
  // 執行篩選
  applyFilter(filterType);
}

/**
 * 應用篩選
 * @param {string} filterType - 篩選類型
 */
function applyFilter(filterType) {
  if (!filterType || filterType === 'all') {
    filteredOrders = [...orders];
  } else {
    filteredOrders = orders.filter(order => order.status === filterType);
  }
  
  console.log(`Filter applied: ${filterType}, ${filteredOrders.length} orders found`);
  renderOrders(filteredOrders);
}

/**
 * 防抖搜尋
 */
const debounceSearch = debounce(performSearch, 300);

/**
 * 執行搜尋
 */
function performSearch() {
  const searchInput = $('.search-input');
  if (!searchInput) return;

  const searchTerm = searchInput.value.trim().toLowerCase();
  
  if (!searchTerm) {
    // 如果搜尋詞為空，顯示所有訂單
    filteredOrders = [...orders];
  } else {
    // 根據訂單編號和專案名稱進行搜尋
    filteredOrders = orders.filter(order => {
      const orderNumberMatch = order.order_number.toLowerCase().includes(searchTerm);
      const projectNameMatch = order.project_name && 
                              order.project_name.toLowerCase().includes(searchTerm);
      return orderNumberMatch || projectNameMatch;
    });
  }
  
  console.log(`Search results: ${filteredOrders.length} orders found`);
  renderOrders(filteredOrders);
}

/**
 * 處理訂單卡片點擊
 * @param {Event} event - 點擊事件
 */
function handleOrderCardClick(event) {
  const viewBtn = event.target.closest('.view-details-btn');
  if (!viewBtn) return;

  const orderId = viewBtn.dataset.orderId;
  if (orderId) {
    // 可以跳轉到訂單詳情頁面或顯示詳情彈窗
    console.log('View order details:', orderId);
    // window.location.href = `order-details.html?id=${orderId}`;
  }
}

/**
 * 顯示登入要求
 */
function showLoginRequired() {
  const container = $('.orders-container');
  if (!container) return;

  const loginRequiredDiv = createElement('div', {
    className: 'login-required-message',
    style: 'text-align: center; padding: 2rem; color: #666;'
  }, [
    createElement('p', {}, '請先登入以查看訂單記錄'),
    createElement('button', {
      className: 'btn btn-primary',
      onclick: () => {
        // 觸發登入流程
        console.log('Trigger login');
      }
    }, '立即登入')
  ]);
  
  container.innerHTML = '';
  container.appendChild(loginRequiredDiv);
}

/**
 * 顯示載入狀態
 * @param {boolean} show - 是否顯示載入狀態
 */
function showLoading(show) {
  const container = $('.orders-container');
  if (!container) return;

  let loadingDiv = $('.loading-message');
  
  if (show) {
    if (!loadingDiv) {
      loadingDiv = createElement('div', {
        className: 'loading-message',
        style: 'text-align: center; padding: 2rem; color: #666;'
      }, '載入中...');
      container.appendChild(loadingDiv);
    }
  } else {
    if (loadingDiv) {
      loadingDiv.remove();
    }
  }
}

/**
 * 顯示錯誤訊息
 * @param {string} message - 錯誤訊息
 */
function showError(message) {
  const container = $('.orders-container');
  if (!container) return;

  const errorDiv = createElement('div', {
    className: 'error-message',
    style: 'text-align: center; padding: 2rem; color: #f44336; background: #ffebee; border-radius: 4px; margin: 1rem 0;'
  }, message);
  
  container.appendChild(errorDiv);
  
  // 5秒後自動移除
  setTimeout(() => {
    if (errorDiv.parentNode) {
      errorDiv.remove();
    }
  }, 5000);
}

/**
 * 防抖函數
 * @param {Function} func - 要防抖的函數
 * @param {number} wait - 等待時間
 * @returns {Function} 防抖後的函數
 */
function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

// 當 DOM 載入完成時初始化訂單歷史頁面
ready(() => {
  // 檢查是否為訂單歷史頁面
  const isOrderHistoryPage = window.location.pathname.includes('order-history.html');
  
  if (isOrderHistoryPage) {
    initializeOrderHistoryPage();
  }
});

// 導出函數供其他模組使用
export { initializeOrderHistoryPage };
