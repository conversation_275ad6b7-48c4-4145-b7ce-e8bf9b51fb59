<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>身份驗證測試</title>
    <style>
        body {
            font-family: monospace;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        .test-button {
            background-color: #57AC5A;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        .test-result {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 12px;
            margin-top: 12px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .success { border-color: #57AC5A; background-color: #f0f8f0; color: #2d5a2f; }
        .error { border-color: #e74c3c; background-color: #fdf2f2; color: #721c24; }
        .info { border-color: #3498db; background-color: #f0f8ff; color: #1e3a8a; }
    </style>
</head>
<body>
    <h1>身份驗證測試頁面</h1>
    
    <div class="test-container">
        <h2>管理員身份驗證測試</h2>
        <button class="test-button" onclick="testValidAdmin()">測試有效管理員</button>
        <button class="test-button" onclick="testInvalidAdmin()">測試無效管理員</button>
        <button class="test-button" onclick="testCurrentAuth()">檢查當前身份</button>
        <button class="test-button" onclick="testLogout()">登出測試</button>
        <div class="test-result" id="auth-result">點擊按鈕開始測試...</div>
    </div>
    
    <div class="test-container">
        <h2>訂單資料存取測試</h2>
        <button class="test-button" onclick="testFetchOrders()">測試載入訂單</button>
        <button class="test-button" onclick="testOrderDetails()">測試訂單詳情</button>
        <div class="test-result" id="data-result">點擊按鈕開始測試...</div>
    </div>
    
    <div class="test-container">
        <h2>快速導航</h2>
        <a href="admin-login.html" class="test-button" style="text-decoration: none; display: inline-block;">管理員登入</a>
        <a href="management/order-management.html" class="test-button" style="text-decoration: none; display: inline-block;">訂單管理</a>
        <a href="test-orders.html" class="test-button" style="text-decoration: none; display: inline-block;">測試頁面</a>
    </div>

    <!-- Supabase CDN -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    
    <script type="module">
        import {
            initializeSupabase,
            authenticateAsAdmin,
            isCurrentUserAdmin,
            getCurrentAdminUser,
            logoutAdmin,
            fetchAllOrders,
            fetchOrderDetails
        } from '../js/services/supabaseService.js';
        
        let testOrderId = null;
        
        // 初始化
        document.addEventListener('DOMContentLoaded', () => {
            initializeSupabase();
        });
        
        // 測試有效管理員
        window.testValidAdmin = async function() {
            const resultDiv = document.getElementById('auth-result');
            resultDiv.textContent = '正在測試有效管理員身份驗證...';
            resultDiv.className = 'test-result info';
            
            try {
                const success = await authenticateAsAdmin('admin_test_user');
                
                if (success) {
                    const user = getCurrentAdminUser();
                    resultDiv.textContent = `✅ 管理員身份驗證成功！\n\n用戶資訊：\n${JSON.stringify(user, null, 2)}`;
                    resultDiv.className = 'test-result success';
                } else {
                    resultDiv.textContent = '❌ 管理員身份驗證失敗';
                    resultDiv.className = 'test-result error';
                }
            } catch (error) {
                resultDiv.textContent = `❌ 身份驗證異常：\n\n${error.message}`;
                resultDiv.className = 'test-result error';
            }
        };
        
        // 測試無效管理員
        window.testInvalidAdmin = async function() {
            const resultDiv = document.getElementById('auth-result');
            resultDiv.textContent = '正在測試無效管理員身份驗證...';
            resultDiv.className = 'test-result info';
            
            try {
                const success = await authenticateAsAdmin('invalid_user');
                
                if (!success) {
                    resultDiv.textContent = '✅ 正確拒絕了無效管理員\n\n這是預期的行為。';
                    resultDiv.className = 'test-result success';
                } else {
                    resultDiv.textContent = '❌ 錯誤地接受了無效管理員';
                    resultDiv.className = 'test-result error';
                }
            } catch (error) {
                resultDiv.textContent = `❌ 身份驗證異常：\n\n${error.message}`;
                resultDiv.className = 'test-result error';
            }
        };
        
        // 檢查當前身份
        window.testCurrentAuth = function() {
            const resultDiv = document.getElementById('auth-result');
            
            if (isCurrentUserAdmin()) {
                const user = getCurrentAdminUser();
                resultDiv.textContent = `✅ 當前已登入管理員：\n\n${JSON.stringify(user, null, 2)}`;
                resultDiv.className = 'test-result success';
            } else {
                resultDiv.textContent = '⚠️ 當前未登入管理員';
                resultDiv.className = 'test-result info';
            }
        };
        
        // 登出測試
        window.testLogout = function() {
            const resultDiv = document.getElementById('auth-result');
            
            logoutAdmin();
            resultDiv.textContent = '✅ 已登出管理員身份\n\n請重新測試身份驗證。';
            resultDiv.className = 'test-result success';
        };
        
        // 測試載入訂單
        window.testFetchOrders = async function() {
            const resultDiv = document.getElementById('data-result');
            resultDiv.textContent = '正在測試載入訂單...';
            resultDiv.className = 'test-result info';
            
            try {
                // 確保已登入
                if (!isCurrentUserAdmin()) {
                    const authSuccess = await authenticateAsAdmin();
                    if (!authSuccess) {
                        throw new Error('管理員身份驗證失敗');
                    }
                }
                
                const result = await fetchAllOrders({}, { page: 1, limit: 3 });
                
                if (result.orders && result.orders.length > 0) {
                    testOrderId = result.orders[0].id;
                    
                    const orderSummary = result.orders.map(order => 
                        `${order.order_number} - ${order.user_name} - ${order.status} - $${order.final_amount}`
                    ).join('\n');
                    
                    resultDiv.textContent = `✅ 成功載入 ${result.orders.length} 筆訂單（共 ${result.totalCount} 筆）：\n\n${orderSummary}`;
                    resultDiv.className = 'test-result success';
                } else {
                    resultDiv.textContent = '⚠️ 沒有找到訂單資料';
                    resultDiv.className = 'test-result info';
                }
            } catch (error) {
                resultDiv.textContent = `❌ 載入訂單失敗：\n\n${error.message}`;
                resultDiv.className = 'test-result error';
            }
        };
        
        // 測試訂單詳情
        window.testOrderDetails = async function() {
            const resultDiv = document.getElementById('data-result');
            
            if (!testOrderId) {
                resultDiv.textContent = '⚠️ 請先執行「測試載入訂單」以獲取訂單ID';
                resultDiv.className = 'test-result info';
                return;
            }
            
            resultDiv.textContent = '正在測試訂單詳情...';
            resultDiv.className = 'test-result info';
            
            try {
                const orderDetails = await fetchOrderDetails(testOrderId);
                
                const details = `訂單編號：${orderDetails.order_number}
客戶：${orderDetails.user_name}
專案：${orderDetails.project_name}
總金額：$${orderDetails.total_amount}
最終金額：$${orderDetails.final_amount}
狀態：${orderDetails.status}
訂單項目數量：${orderDetails.items.length}`;
                
                resultDiv.textContent = `✅ 訂單詳情載入成功：\n\n${details}`;
                resultDiv.className = 'test-result success';
            } catch (error) {
                resultDiv.textContent = `❌ 載入訂單詳情失敗：\n\n${error.message}`;
                resultDiv.className = 'test-result error';
            }
        };
    </script>
</body>
</html>
