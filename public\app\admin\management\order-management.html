<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>小森活後台 - 訂單管理</title>
    <link rel="stylesheet" href="../../shared-styles.css">
    <style>
        /* 後台特定樣式 */
        body {
            min-height: 100vh;
            display: flex;
        }

        /* 側邊導航 */
        .sidebar {
            width: 250px;
            background-color: var(--bg-white);
            box-shadow: var(--shadow-md);
            padding: var(--space-lg) 0;
            position: fixed;
            height: 100vh;
            overflow-y: auto;
            z-index: 10;
        }

        .logo-container {
            padding: 0 var(--space-lg);
            margin-bottom: var(--space-xl);
            display: flex;
            align-items: center;
        }

        .logo {
            width: 40px;
            height: 40px;
            margin-right: var(--space-sm);
            object-fit: contain;
        }

        .logo-text {
            font-size: 1.2rem;
            font-weight: 700;
            color: var(--primary-color);
        }

        .nav-title {
            padding: var(--space-sm) var(--space-lg);
            font-size: 0.75rem;
            font-weight: 600;
            color: var(--text-medium);
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .nav-item {
            display: flex;
            align-items: center;
            padding: var(--space-md) var(--space-lg);
            color: var(--text-medium);
            text-decoration: none;
            transition: all 0.2s ease;
            margin-bottom: var(--space-xs);
        }

        .nav-item.active {
            background-color: var(--primary-light);
            color: var(--primary-color);
            font-weight: 500;
            border-right: 3px solid var(--primary-color);
        }

        .nav-item:hover:not(.active) {
            background-color: rgba(0, 0, 0, 0.03);
        }

        .nav-icon {
            margin-right: var(--space-md);
            width: 20px;
            height: 20px;
        }

        /* 主内容 */
        .main-content {
            flex: 1;
            margin-left: 250px;
            padding: 2rem;
        }

        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
        }

        .page-title {
            font-size: 1.8rem;
            font-weight: 700;
            color: #333;
        }

        /* 搜尋與過濾 */
        .filter-container {
            background-color: white;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            padding: 1.5rem;
            margin-bottom: 2rem;
        }

        .filter-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .filter-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #444;
        }

        .filter-actions {
            display: flex;
            gap: 0.5rem;
        }

        .filter-row {
            display: flex;
            flex-wrap: wrap;
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .filter-group {
            flex: 1;
            min-width: 200px;
        }

        .filter-label {
            display: block;
            font-size: 0.9rem;
            font-weight: 500;
            margin-bottom: 0.5rem;
            color: #666;
        }

        .filter-input {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #ddd;
            border-radius: 8px;
            font-size: 1rem;
            transition: all 0.2s ease;
        }

        .filter-input:focus {
            outline: none;
            border-color: #57AC5A;
            box-shadow: 0 0 0 3px rgba(87, 172, 90, 0.1);
        }

        .filter-select {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #ddd;
            border-radius: 8px;
            font-size: 1rem;
            appearance: none;
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23666666' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
            background-repeat: no-repeat;
            background-position: right 0.75rem center;
            background-size: 16px;
        }

        .filter-select:focus {
            outline: none;
            border-color: #57AC5A;
            box-shadow: 0 0 0 3px rgba(87, 172, 90, 0.1);
        }

        /* 按鈕樣式 */
        .btn {
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            font-weight: 500;
            border: none;
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 1rem;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }

        .btn-icon {
            margin-right: 0.5rem;
        }

        .btn-primary {
            background-color: #57AC5A;
            color: white;
            box-shadow: 0 2px 6px rgba(87, 172, 90, 0.2);
        }

        .btn-primary:hover {
            background-color: #aedfc0;
            box-shadow: 0 4px 12px rgba(87, 172, 90, 0.3);
        }

        .btn-outline {
            background-color: transparent;
            color: #57AC5A;
            border: 2px solid #57AC5A;
        }

        .btn-outline:hover {
            background-color: rgba(87, 172, 90, 0.05);
        }

        .btn-sm {
            padding: 0.5rem 1rem;
            font-size: 0.9rem;
        }

        /* Checkbox Styles */
        .list-checkbox {
            width: 20px;
            height: 20px;
            cursor: pointer;
        }

        /* 訂單列表 */
        .order-list {
            background-color: white;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            overflow: hidden;
            margin-bottom: 2rem;
        }

        .list-header {
            display: grid;
            /* Added checkbox column */
            grid-template-columns: 40px 140px 1fr 120px 120px 120px 120px 100px;
            gap: 1rem;
            padding: 1rem 1.5rem;
            background-color: #f9f9f9;
            font-weight: 500;
            color: #444;
            border-bottom: 1px solid #eee;
        }

        .order-row {
            display: grid;
             /* Added checkbox column */
            grid-template-columns: 40px 140px 1fr 120px 120px 120px 120px 100px;
            gap: 1rem;
            padding: 1rem 1.5rem;
            align-items: center;
            border-bottom: 1px solid #eee;
            transition: background-color 0.2s ease;
        }

        .order-row:hover {
            background-color: #f9f9f9;
        }

        .order-id {
            font-weight: 500;
            color: #444;
        }

        .order-customer {
            display: flex;
            flex-direction: column;
        }

        .customer-name {
            font-weight: 500;
        }

        .customer-id {
            font-size: 0.8rem;
            color: #888;
        }

        .order-amount {
            font-weight: 600;
            color: #2d3748;
        }

        .order-discount {
            color: #e74c3c;
            font-weight: 500;
        }

        .order-status {
            display: inline-flex;
            padding: 0.25rem 0.75rem;
            border-radius: 50px;
            font-size: 0.8rem;
            font-weight: 600;
            text-align: center;
        }

        .status-pending {
            background-color: rgba(241, 196, 15, 0.1);
            color: #f1c40f;
        }

        .status-confirmed {
            background-color: rgba(52, 152, 219, 0.1);
            color: #3498db;
        }

        .status-completed {
            background-color: rgba(87, 172, 90, 0.1);
            color: #57AC5A;
        }

        .status-cancelled {
            background-color: rgba(231, 76, 60, 0.1);
            color: #e74c3c;
        }

        .order-date {
            color: #666;
            font-size: 0.9rem;
        }

        .order-actions {
            display: flex;
            gap: 0.5rem;
            justify-content: flex-end;
        }

        .action-btn {
            width: 32px;
            height: 32px;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: transparent;
            color: #666;
            border: none;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .action-btn:hover {
            background-color: #f0f0f0;
            color: #333;
        }

        /* 分頁 */
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 0.5rem;
            margin-top: 2rem;
        }

        .page-btn {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 8px;
            border: 1px solid #ddd;
            background-color: white;
            color: #666;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .page-btn.active {
            background-color: #57AC5A;
            color: white;
            border-color: #57AC5A;
        }

        .page-btn:hover:not(.active) {
            border-color: #57AC5A;
            color: #57AC5A;
        }

        /* 響應式設計 */
        @media (max-width: 1200px) {
            .list-header, .order-row {
                 /* Added checkbox column */
                grid-template-columns: 40px 140px 1fr 120px 120px 120px 100px;
            }

            .order-discount {
                display: none;
            }
        }

        @media (max-width: 992px) {
            .sidebar {
                width: 200px;
            }

            .main-content {
                margin-left: 200px;
            }

            .list-header, .order-row {
                 /* Added checkbox column */
                grid-template-columns: 40px 120px 1fr 100px 100px 80px;
            }

            .order-discount, .order-date {
                display: none;
            }
        }

        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }

            .sidebar.active {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
                padding: 1rem;
            }

            .list-header {
                 display: none; /* Hide header on mobile */
            }
            .order-row {
                 /* Added checkbox column */
                grid-template-columns: 40px 1fr auto; /* Checkbox, Info, Actions */
                padding: 0.75rem;
                font-size: 0.9rem;
                gap: 0.5rem;
            }
            /* Mobile label adjustments */
            .order-row > div:not(:first-child):not(:last-child)::before {
                content: attr(data-label);
                font-weight: bold;
                display: block;
                margin-bottom: 0.25rem;
                color: #555;
                font-size: 0.8rem;
            }
            .order-id::before { content: "訂單編號: "; }
            .order-customer::before { content: "客戶: "; }
            .order-amount::before { content: "金額: "; }
            .order-discount::before { content: "折扣: "; }
            .order-status-container::before { content: "狀態: "; }
            .order-date::before { content: "日期: "; }

            /* Stack info in the second column */
             .order-id, .order-customer, .order-amount, .order-discount, .order-status-container, .order-date {
                 grid-column: 2 / 3;
             }
             .order-actions {
                 grid-column: 3 / 4;
                 align-self: center;
             }
             /* Hide elements already hidden in larger views or redundant */
            /* } */ /* Removed misplaced closing brace */

            .order-amount, .order-status { /* Moved inside the @media block */
                display: none;
            }

            .filter-row {
                flex-direction: column;
            }

            .filter-group {
                width: 100%;
            }

            .page-btn {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
        } /* Correct closing brace for @media (max-width: 768px) */
    </style>
</head>
<body>
    <!-- 側邊導航 -->
    <aside class="sidebar">
        <div class="logo-container">
            <img src="../../user/forest-life-logo.png" alt="小森活" class="logo">
            <div class="logo-text">小森活管理系統</div>
        </div>

        <div class="nav-title">主選單</div>
        <a href="../dashboard.html" class="nav-item">
            <svg class="nav-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <rect x="3" y="3" width="7" height="9" />
                <rect x="14" y="3" width="7" height="5" />
                <rect x="14" y="12" width="7" height="9" />
                <rect x="3" y="16" width="7" height="5" />
            </svg>
            儀表板
        </a>
        <a href="projects.html" class="nav-item">
            <svg class="nav-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2" />
                <rect x="8" y="2" width="8" height="4" rx="1" ry="1" />
            </svg>
            專案管理
        </a>
        <a href="order-management.html" class="nav-item active">
            <svg class="nav-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" />
                <path d="M14 2v6h6" />
                <path d="M16 13H8" />
                <path d="M16 17H8" />
                <path d="M10 9H8" />
            </svg>
            訂單管理
        </a>
        <a href="user-management.html" class="nav-item">
            <svg class="nav-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2" />
                <circle cx="12" cy="7" r="4" />
            </svg>
            用戶管理
        </a>
    </aside>

    <!-- 主內容 -->
    <main class="main-content">
        <div class="page-header">
            <h1 class="page-title">訂單管理</h1>
            <div class="user-control">
                <div class="user-avatar">
                    <img src="../../user/forest-life-logo.png" alt="用戶頭像">
                </div>
            </div>
        </div>

        <!-- 搜尋與過濾 -->
        <div class="filter-container">
            <div class="filter-header">
                <h3 class="filter-title">搜尋與過濾</h3>
                <div class="filter-actions">
                    <button class="btn btn-outline btn-sm">重置</button>
                </div>
            </div>

            <div class="filter-row">
                <div class="filter-group">
                    <label class="filter-label">訂單編號</label>
                    <input type="text" class="filter-input" placeholder="輸入訂單編號">
                </div>

                <div class="filter-group">
                    <label class="filter-label">用戶名稱</label>
                    <input type="text" class="filter-input" placeholder="輸入用戶名稱">
                </div>

                <div class="filter-group">
                    <label class="filter-label">專案</label>
                    <select class="filter-select" data-filter="project">
                        <option value="">全部專案</option>
                    </select>
                </div>
            </div>

            <div class="filter-row">
                <div class="filter-group">
                    <label class="filter-label">訂單狀態</label>
                    <select class="filter-select" data-filter="status">
                        <option value="">全部狀態</option>
                        <option value="Pending">待處理</option>
                        <option value="Confirmed">已確認</option>
                        <option value="Completed">已完成</option>
                        <option value="Cancelled">已取消</option>
                    </select>
                </div>

                <div class="filter-group">
                    <label class="filter-label">訂單日期 (開始)</label>
                    <input type="date" class="filter-input">
                </div>

                <div class="filter-group">
                    <label class="filter-label">訂單日期 (結束)</label>
                    <input type="date" class="filter-input">
                </div>
            </div>

            <div class="filter-row">
                <div class="filter-group">
                    <button class="btn btn-primary">搜尋</button>
                </div>
            </div>
        </div>

        <!-- Batch Actions Section -->
        <div class="batch-actions-container" style="margin-bottom: 1.5rem; display: flex; justify-content: flex-end; gap: 1rem;">
             <button class="btn btn-outline btn-sm" disabled>更新選取狀態</button>
             <button class="btn btn-danger-outline btn-sm" disabled style="border-color: #e74c3c; color: #e74c3c;">刪除選取項目</button>
             <!-- Add btn-danger-outline style -->
             <style>
                 .btn-danger-outline { background-color: transparent; color: #e74c3c; border: 2px solid #e74c3c; }
                 .btn-danger-outline:hover:not(:disabled) { background-color: rgba(231, 76, 60, 0.05); }
                 .btn:disabled { opacity: 0.6; cursor: not-allowed; }
             </style>
        </div>

        <!-- 訂單列表 -->
        <div class="order-list">
            <div class="list-header">
                <div><input type="checkbox" class="list-checkbox" title="全選"></div>
                <div>訂單編號</div>
                <div>客戶</div>
                <div>金額</div>
                <div>折扣</div>
                <div>狀態</div>
                <div>日期</div>
                <div>操作</div>
            </div>

            <!-- 訂單資料將由 JavaScript 動態載入 -->
        </div>

        <!-- 分頁 -->
        <div class="pagination">
            <button class="page-btn">&lt;</button>
            <button class="page-btn active">1</button>
            <button class="page-btn">2</button>
            <button class="page-btn">3</button>
            <button class="page-btn">&gt;</button>
        </div>
    </main>

    <!-- Supabase CDN -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>

    <!-- 引入 JavaScript 模組 -->
    <script type="module" src="../../js/pages/admin/orderManagement.js"></script>

    <!-- 開發用：前台跳轉按鈕 -->
    <div style="position: fixed; bottom: 20px; right: 20px; z-index: 1000;">
        <a href="../../user/index.html" target="_blank"
           style="display: flex; align-items: center; justify-content: center; width: 50px; height: 50px;
                  background-color: #57AC5A; color: white; border-radius: 50%; text-decoration: none;
                  box-shadow: 0 4px 12px rgba(87, 172, 90, 0.3); transition: all 0.3s ease;"
           title="前台首頁（開發用）"
           onmouseover="this.style.transform='scale(1.1)'"
           onmouseout="this.style.transform='scale(1)'">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
                <polyline points="9,22 9,12 15,12 15,22"></polyline>
            </svg>
        </a>
    </div>
</body>
</html>
