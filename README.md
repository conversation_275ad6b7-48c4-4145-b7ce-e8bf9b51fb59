這是一份針對 LLM（大型語言模型）的詳細開發指引，旨在協助你完成「小森活預購系統」的重構。

**專案目標：**

快速重構現有的「小森活預購系統」。新系統將採用混合架構：

*   **使用者前端 (User Interface)**：位於 `public/app/user/` 和 `public/app/index.html`，基於現有的 HTML、CSS (`public/app/shared-styles.css`, `public/app/user/home.css` 等) 和 Vanilla JavaScript。
*   **管理後台 (Admin Panel)**：位於 `public/app/admin/`，同樣基於獨立的 HTML、CSS 和 Vanilla JavaScript。
*   **核心交互與狀態管理**：由於 `prototypes/` (現為 `public/app/`) 內的 HTML/JS 頁面之間存在複雜交互和共享狀態，我們將引入輕量級客戶端狀態管理方案 (如 Nano Stores) 並組織 JavaScript 模組。
*   **後端服務**：繼續使用 Supabase (資料庫、RPC 函數、觸發器、RLS)。
*   **圖片儲存**：繼續使用 Cloudinary。
*   **開發與建置工具**：Vite。

**你的角色 (LLM)：**

你將作為我的程式碼助手，協助我編寫、重構和組織 Vanilla JavaScript 程式碼，以及處理與 Supabase 和 Cloudinary 的互動。你需要嚴格參考我提供的文件和現有程式碼結構。

**核心參考文件 (位於 `previous_ref/` 目錄)：**

這些文件是你理解系統架構、資料庫和業務邏輯的**最重要依據**。請務必優先參考它們：

1.  **`previous_ref/design-readme.md`**: 包含完整的資料庫表結構、欄位說明、業務規則、核心功能描述等。這是理解系統需求的基石。
2.  **`previous_ref/schema_only.sql`**: 這是 Supabase 資料庫結構的 SQL 匯出，提供了最精確的表定義和關係。
3.  **`previous_ref/supabase-rpc-triggers.sql`**: 包含了所有後端 Supabase 的遠程過程調用 (RPC) 函數和觸發器的 SQL 定義。前端的 Vanilla JS 將會呼叫這些 RPC。
4.  **`previous_ref/row-level-security.md`**: 詳細說明了 Supabase 資料表的行級安全 (RLS) 策略。
5.  **`previous_ref/database-triggers.md`**: 專案狀態自動更新等資料庫觸發器的詳細邏輯。
6.  **`previous_ref/note.md`**: 提示了 `previous_ref/` 中文件的重要性。

**其他參考文件與程式碼：**

*   **`previous_ref/supabase.ts`**: 舊專案中用於與 Supabase 互動的 TypeScript 檔案。你需要將其中**與 React 無關的純邏輯** (例如 `fetchActiveProjects`, `fetchProjectDiscounts`, `createOrderWithItems`, `generateDisplayIdByRPC`) 改寫成 Vanilla JavaScript，並在新專案的服務層 (`public/app/js/services/supabaseService.js`) 中使用。
*   **`previous_ref/cloudinary.ts`**: 舊專案中 Cloudinary 的配置。你需要參考它在新專案的服務層 (`public/app/js/services/cloudinaryService.js`) 中實現圖片上傳邏輯。
*   **`previous_ref/dateUtils.ts`** 和 **`previous_ref/discountCalculator.ts`**: 這些是純邏輯的工具函式，可以幾乎原樣複製到新專案的 `public/app/js/utils/` 目錄下使用。
*   **`.env.example`**: 提供了所有必要的環境變數名稱。實際的值在我本地的 `.env` 檔案中。
*   **`public/app/` 目錄**: 包含所有使用者前端和管理後台的 HTML 原型頁面及相關的 CSS (如 `shared-styles.css`, `home.css` 等)。**這是我們新系統前端的主要構成部分。**

**開發任務與指南：**

**1. JavaScript 模組化與架構建立 (在 `public/app/js/` 目錄下)**

*   **目標**：將原型 HTML 檔案中內聯的或分散的 JavaScript 程式碼，重構成模組化的 `.js` 檔案。
*   **結構建議**：
    ```
    public/app/js/
    ├── store/              // 狀態管理 (使用 Nano Stores 或類似方案)
    │   ├── cartStore.js    // 管理購物車狀態
    │   └── authStore.js    // 管理使用者認證狀態 (LIFF Profile, Admin 登入狀態)
    ├── services/           // 封裝與外部服務的互動
    │   ├── supabaseService.js // 初始化 Supabase Client, 封裝資料庫操作函式 (參考 previous_ref/supabase.ts)
    │   ├── cloudinaryService.js // 圖片上傳邏輯 (參考 previous_ref/cloudinary.ts)
    │   └── liffService.js    // 初始化 LIFF SDK, 封裝 LIFF 操作
    ├── pages/              // 各 HTML 頁面對應的主要 JavaScript 邏輯檔案
    │   ├── user/           // 使用者前端頁面的 JS
    │   │   ├── home.js
    │   │   ├── preorder.js   (你已經有 public/app/user/preorder.js)
    │   │   └── orderHistory.js
    │   └── admin/          // 管理後台頁面的 JS
    │       ├── dashboard.js
    │       ├── projectManagement.js
    │       └── orderManagement.js
    ├── utils/              // 通用工具函式
    │   ├── dateUtils.js    (可從 previous_ref/dateUtils.ts 改寫或直接用)
    │   ├── discountCalculator.js (可從 previous_ref/discountCalculator.ts 改寫或直接用)
    │   └── domUtils.js     // (可選) 封裝常用的 DOM 操作函式
    └── main.js             // 全局初始化腳本，例如 LIFF 初始化、共用事件監聽器等
    ```
*   **要求**：
    *   所有 JavaScript 檔案都應使用 ES6 模組 (`import`/`export`)。
    *   在 HTML 檔案中使用 `<script type="module" src="path/to/module.js"></script>` 引入。
    *   避免使用全域變數，優先使用模組作用域和狀態管理器。

**2. 狀態管理 (Nano Stores 或類似方案)**

*   **目標**：解決 `public/app/` 中各 HTML 頁面間複雜的交互和共享狀態問題。
*   **任務**：
    *   **選擇並引入狀態管理工具**：我傾向於使用 Nano Stores。請協助我將其引入到 `public/app/js/lib/` (如果使用 CDN 或直接複製原始碼) 或指導如何在 Vanilla JS 環境中正確安裝和使用。
    *   **設計 Store 結構**:
        *   `cartStore.js`: 應包含購物車內的商品列表 (`items`)、加入商品 (`addItem`)、更新數量 (`updateItemQuantity`)、移除商品 (`removeItem`)、清空購物車 (`clearCart`)、計算總商品數 (`totalCount`)、計算小計 (`subtotal`) 等。考慮使用 `@nanostores/persistent` 將購物車狀態持久化到 `localStorage`。
        *   `authStore.js`: 應包含目前使用者資訊 (LIFF Profile, 管理員角色等)、登入狀態 (`isLoggedIn`, `isAdminLoggedIn`)、登入 (`login`)、登出 (`logout`) 等方法。
    *   **編寫 Store 邏輯**: 實現上述 Store 的狀態和操作函數。
    *   **在頁面 JS 中使用 Store**:
        *   指導如何在各個 `pages/*.js` 中 `import` store。
        *   如何在 JS 中讀取 store 狀態 (e.g., `cartItems.get()`)。
        *   如何調用 store 的 action 來更新狀態 (e.g., `addItem({id: '123', ...})`)。
        *   如何訂閱 store 的變化 (`cartItems.subscribe(newItems => { /* 更新 DOM */ })`) 以響應式地更新 UI。

**3. Supabase 服務層 (`supabaseService.js`)**

*   **目標**：提供一個集中的介面來與 Supabase 互動。
*   **任務**：
    *   **初始化 Client**: 使用 `.env` 中的 `VITE_SUPABASE_URL` 和 `VITE_SUPABASE_ANON_KEY` 初始化 Supabase JavaScript Client。
    *   **移植和改寫函式**:
        *   從 `previous_ref/supabase.ts` 中提取 `fetchActiveProjects`, `fetchProjectDiscounts`, `createOrderWithItems`, `fetchUserOrders`, `generateDisplayIdByRPC` 等函式的核心邏輯。
        *   將它們改寫成能在 Vanilla JS 環境中運行的非同步函式。
        *   確保所有資料庫查詢都符合 `previous_ref/row-level-security.md` 中定義的 RLS 策略。
        *   對於 RPC 呼叫，參考 `previous_ref/supabase-rpc-triggers.sql` 來確定函數名稱和參數。
    *   **錯誤處理**: 統一處理 Supabase API 返回的錯誤。

**4. Cloudinary 服務層 (`cloudinaryService.js`)**

*   **目標**：處理圖片上傳到 Cloudinary。
*   **任務**：
    *   參考 `previous_ref/cloudinary.ts` 初始化 Cloudinary 或直接使用其上傳 URL。
    *   實現一個 `uploadImage(file)` 函式，該函式接收一個 `File` 對象，將其上傳到 Cloudinary (使用 `fetch` API 和 `FormData`)，並返回圖片的 URL。
    *   可以參考 `previous_ref/image-upload-and-frontend-backend-integration.md` 中的 Cloudinary 配置建議。

**5. LIFF 服務層 (`liffService.js`)**

*   **目標**：封裝 LIFF SDK 的初始化和常用操作。
*   **任務**：
    *   **初始化 LIFF**: 使用 `.env` 中的 `VITE_LIFF_ID`。
    *   **登入/登出**: 封裝 `liff.login()` 和 `liff.logout()`。
    *   **獲取用戶資料**: 封裝 `liff.getProfile()`，並將獲取到的用戶資訊存儲到 `authStore.js`。
    *   **檢查登入狀態**: 封裝 `liff.isLoggedIn()`。

**6. 頁面邏輯實現 (在 `public/app/js/pages/**/*.js` 中)**

*   **目標**：為 `public/app/` 下的每個 HTML 頁面編寫對應的 JavaScript 邏輯，使其能夠：
    *   從 `services/*.js` 獲取數據。
    *   從 `store/*.js` 讀取和更新共享狀態。
    *   基於數據和狀態動態更新 DOM 內容。
    *   處理用戶交互事件 (點擊、輸入等)。
*   **通用任務**：
    *   **DOM 元素選擇**: 使用 `document.getElementById`, `document.querySelector` 等。
    *   **事件監聽**: 使用 `element.addEventListener`。
    *   **DOM 更新**: 手動操作 DOM (例如 `element.innerHTML`, `element.textContent`, `element.appendChild`, `element.classList.add/remove` 等)。你可以創建一些 `domUtils.js` 來封裝常用的 DOM 更新模式。
*   **具體頁面範例**：
    *   **`user/home.js`**:
        *   呼叫 `supabaseService.fetchActiveProjects()` 獲取專案列表。
        *   動態渲染專案卡片到 `public/app/user/index.html` 的對應區域。
        *   處理搜尋和篩選邏輯 (如果有的話)。
    *   **`user/preorder.js`**:
        *   根據 URL 參數獲取專案 ID，然後呼叫 `supabaseService` 獲取專案詳情和商品列表。
        *   渲染商品卡片。
        *   處理商品數量的增減，並更新 `cartStore`。
        *   從 `cartStore` 讀取購物車內容，呼叫 `utils/discountCalculator.js` (需要從 `previous_ref` 改寫) 計算折扣和總價，並更新訂單摘要區域。
        *   處理訂單提交：收集備註、取貨日期，呼叫 `supabaseService.createOrderWithItems()`。
    *   **`admin/projectManagement.js`**:
        *   獲取專案列表並渲染到表格。
        *   實現新增/編輯專案的表單邏輯 (彈窗或頁內表單)，提交時呼叫 `supabaseService`。
        *   處理刪除專案。
        *   處理圖片上傳 (呼叫 `cloudinaryService`)。
        *   專案亮點、預設折扣等邏輯參考 `previous_ref/design-readme.md`。

**7. 樣式與視覺**

*   主要依賴 `public/app/shared-styles.css` 和各頁面/組件的特定 CSS。
*   確保 Vanilla JS 在更新 DOM 時能正確應用這些樣式。
*   視覺效果參考 `previous_ref/visual-guide.html` (如果適用)。

**開發過程中的注意事項：**

*   **逐步進行**：先完成基礎建設 (JS 模組化、服務層初始化)，然後逐個頁面實現核心功能。
*   **錯誤處理**：在所有 API 呼叫和關鍵邏輯中加入適當的錯誤處理，並將錯誤信息反饋給使用者。
*   **使用者體驗**：即使是 Vanilla JS，也要考慮互動的流暢性和響應性 (例如，操作時顯示 loading 狀態)。
*   **安全性**：所有 Supabase 操作都必須通過後端的 RLS 和可能的 RPC 函數進行權限控制。不要在前端暴露敏感操作。
*   **日誌**：在關鍵步驟使用 `console.log` 進行調試。
