/**
 * 管理員登入頁面 JavaScript 邏輯
 * 負責管理員身份驗證和權限檢查
 */

import { login, isLoggedIn, getProfile } from '../../services/liffService.js';
import { userInfo, isAdminLoggedIn, hasAdminPermission } from '../../store/authStore.js';
import { $, $$, createElement, setContent, addClass, removeClass, on, ready, show, hide } from '../../utils/domUtils.js';

/**
 * 初始化管理員登入頁面
 */
async function initializeAdminLoginPage() {
  console.log('Initializing admin login page...');
  
  try {
    // 設置事件監聽器
    setupEventListeners();
    
    // 設置狀態監聽器
    setupStoreListeners();
    
    // 檢查當前登入狀態
    checkCurrentLoginStatus();
    
    console.log('Admin login page initialized successfully');
  } catch (error) {
    console.error('Error initializing admin login page:', error);
    showError('頁面初始化失敗，請重新整理頁面');
  }
}

/**
 * 設置事件監聽器
 */
function setupEventListeners() {
  // 登入按鈕
  const loginBtn = $('#loginBtn');
  if (loginBtn) {
    on(loginBtn, 'click', handleLogin);
  }

  // 進入後台按鈕
  const enterAdminBtn = $('#enterAdminBtn');
  if (enterAdminBtn) {
    on(enterAdminBtn, 'click', handleEnterAdmin);
  }

  // 登出按鈕
  const logoutBtn = $('#logoutBtn');
  if (logoutBtn) {
    on(logoutBtn, 'click', handleLogout);
  }
}

/**
 * 設置狀態監聽器
 */
function setupStoreListeners() {
  // 監聽用戶登入狀態變化
  userInfo.subscribe((user) => {
    updateLoginUI(user);
  });

  // 監聽管理員權限狀態
  isAdminLoggedIn.subscribe((isAdmin) => {
    updateAdminUI(isAdmin);
  });
}

/**
 * 檢查當前登入狀態
 */
function checkCurrentLoginStatus() {
  const user = userInfo.get();
  
  if (user) {
    console.log('User already logged in:', user.displayName);
    updateLoginUI(user);
  } else {
    console.log('User not logged in');
    showLoginForm();
  }
}

/**
 * 處理登入
 */
function handleLogin() {
  try {
    console.log('Attempting to login...');
    
    // 顯示載入狀態
    const loginBtn = $('#loginBtn');
    if (loginBtn) {
      loginBtn.disabled = true;
      loginBtn.textContent = '登入中...';
    }
    
    // 觸發 LIFF 登入
    login();
    
  } catch (error) {
    console.error('Login failed:', error);
    showError('登入失敗，請稍後再試');
    
    // 恢復按鈕狀態
    const loginBtn = $('#loginBtn');
    if (loginBtn) {
      loginBtn.disabled = false;
      loginBtn.textContent = '使用 LINE 登入';
    }
  }
}

/**
 * 處理進入後台
 */
function handleEnterAdmin() {
  const user = userInfo.get();
  
  if (!user) {
    showError('請先登入');
    return;
  }
  
  if (!hasAdminPermission()) {
    showError('您沒有管理員權限');
    return;
  }
  
  // 跳轉到管理後台
  window.location.href = 'dashboard.html';
}

/**
 * 處理登出
 */
function handleLogout() {
  try {
    console.log('Logging out...');
    
    // 觸發登出
    import('../../services/liffService.js').then(({ logout }) => {
      logout();
    });
    
  } catch (error) {
    console.error('Logout failed:', error);
    showError('登出失敗，請稍後再試');
  }
}

/**
 * 更新登入 UI
 * @param {Object|null} user - 用戶資訊
 */
function updateLoginUI(user) {
  const loginSection = $('#loginSection');
  const userSection = $('#userSection');
  const userNameElement = $('#userName');
  const userRoleElement = $('#userRole');
  
  if (user) {
    // 顯示用戶資訊
    if (loginSection) hide(loginSection);
    if (userSection) show(userSection);
    
    if (userNameElement) {
      setContent(userNameElement, user.displayName || '未知用戶');
    }
    
    if (userRoleElement) {
      const roleText = getRoleDisplayText(user.role);
      setContent(userRoleElement, roleText);
    }
    
  } else {
    // 顯示登入表單
    if (loginSection) show(loginSection);
    if (userSection) hide(userSection);
  }
}

/**
 * 更新管理員 UI
 * @param {boolean} isAdmin - 是否為管理員
 */
function updateAdminUI(isAdmin) {
  const enterAdminBtn = $('#enterAdminBtn');
  const permissionMessage = $('#permissionMessage');
  
  if (isAdmin) {
    if (enterAdminBtn) {
      show(enterAdminBtn);
      enterAdminBtn.disabled = false;
    }
    if (permissionMessage) {
      setContent(permissionMessage, '您有管理員權限，可以進入後台管理系統');
      removeClass(permissionMessage, 'error');
      addClass(permissionMessage, 'success');
    }
  } else {
    if (enterAdminBtn) {
      hide(enterAdminBtn);
    }
    if (permissionMessage) {
      setContent(permissionMessage, '您沒有管理員權限，無法進入後台管理系統');
      removeClass(permissionMessage, 'success');
      addClass(permissionMessage, 'error');
    }
  }
}

/**
 * 顯示登入表單
 */
function showLoginForm() {
  const loginSection = $('#loginSection');
  const userSection = $('#userSection');
  
  if (loginSection) show(loginSection);
  if (userSection) hide(userSection);
}

/**
 * 獲取角色顯示文字
 * @param {string} role - 用戶角色
 * @returns {string} 角色顯示文字
 */
function getRoleDisplayText(role) {
  switch (role) {
    case 'admin':
      return '系統管理員';
    case 'manager':
      return '專案管理員';
    case 'user':
      return '一般用戶';
    default:
      return '未知角色';
  }
}

/**
 * 顯示錯誤訊息
 * @param {string} message - 錯誤訊息
 */
function showError(message) {
  // 創建錯誤提示
  const errorDiv = createElement('div', {
    className: 'error-message',
    style: `
      position: fixed;
      top: 20px;
      left: 50%;
      transform: translateX(-50%);
      background: #f44336;
      color: white;
      padding: 12px 24px;
      border-radius: 4px;
      z-index: 10000;
      font-size: 14px;
    `
  }, message);
  
  document.body.appendChild(errorDiv);
  
  // 5秒後自動移除
  setTimeout(() => {
    if (errorDiv.parentNode) {
      errorDiv.parentNode.removeChild(errorDiv);
    }
  }, 5000);
}

/**
 * 顯示成功訊息
 * @param {string} message - 成功訊息
 */
function showSuccess(message) {
  // 創建成功提示
  const successDiv = createElement('div', {
    className: 'success-message',
    style: `
      position: fixed;
      top: 20px;
      left: 50%;
      transform: translateX(-50%);
      background: #4caf50;
      color: white;
      padding: 12px 24px;
      border-radius: 4px;
      z-index: 10000;
      font-size: 14px;
    `
  }, message);
  
  document.body.appendChild(successDiv);
  
  // 3秒後自動移除
  setTimeout(() => {
    if (successDiv.parentNode) {
      successDiv.parentNode.removeChild(successDiv);
    }
  }, 3000);
}

// 當 DOM 載入完成時初始化管理員登入頁面
ready(() => {
  // 檢查是否為管理員登入頁面
  const isAdminLoginPage = window.location.pathname.includes('admin/login.html');
  
  if (isAdminLoginPage) {
    initializeAdminLoginPage();
  }
});

// 導出函數供其他模組使用
export { initializeAdminLoginPage };
