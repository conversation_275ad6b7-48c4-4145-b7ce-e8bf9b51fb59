/* --- Preorder Page (Combined Detail, Selection, Summary) Specific Styles --- */

.page-preorder .main-content .container {
    padding-top: var(--space-lg);
    padding-bottom: var(--space-lg);
}

/* --- 1. Project Details Section Styles --- */
.page-preorder .project-details {
    padding: var(--space-xl);
    margin-bottom: var(--space-xl);
    background-color: var(--bg-white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    transition: box-shadow 0.3s ease;
}

.page-preorder .project-details:hover {
    box-shadow: var(--shadow-md);
}

.page-preorder .project-info { margin-bottom: var(--space-xl); }

.page-preorder .project-title { 
    font-size: var(--font-size-xxxl);
    font-weight: 700;
    margin-bottom: var(--space-lg);
    color: var(--text-dark);
    line-height: 1.2;
}

.page-preorder .project-meta { 
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: var(--space-md);
    margin-bottom: var(--space-lg);
}

.page-preorder .project-status {
    padding: var(--space-xs) var(--space-lg);
    font-weight: 500;
    font-size: var(--font-size-base);
    border-radius: var(--radius-full);
    transition: transform 0.2s ease;
}

.page-preorder .project-status:hover {
    transform: translateY(-1px);
}

.page-preorder .status-active {
    background-color: var(--primary-lighter);
    color: var(--primary-dark);
}

.page-preorder .status-ordering_ended {
    background-color: var(--warning-lighter);
    color: var(--warning-dark);
}

.page-preorder .status-arrived {
    background-color: var(--success-lighter);
    color: var(--success-dark);
}

.page-preorder .status-completed {
    background-color: var(--text-lighter);
    color: var(--text-dark);
}

.page-preorder .project-deadline {
    color: var(--text-medium);
    font-size: var(--font-size-base);
    font-weight: 500;
    padding: var(--space-xs) var(--space-md);
    background-color: var(--bg-light);
    border-radius: var(--radius-full);
}

.page-preorder .project-description { 
    color: var(--text-medium);
    line-height: var(--line-height-relaxed);
    font-size: var(--font-size-lg);
    margin-bottom: var(--space-lg);
}

/* Project Highlights */
.page-preorder .project-highlights {
    display: flex;
    flex-wrap: nowrap;
    gap: var(--space-xl);
    margin-bottom: var(--space-lg);
}

.page-preorder .highlight-item {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
}

.page-preorder .highlight-icon {
    font-size: 24px;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--bg-light);
    border-radius: var(--radius-full);
}

.page-preorder .highlight-text {
    font-size: var(--font-size-base);
    color: var(--text-dark);
    font-weight: 500;
}

/* Gallery Styles */
.page-preorder .project-gallery-container {
    width: 100%;
    overflow: hidden;
    background-color: var(--bg-light);
    position: relative;
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-light);
    margin-top: 0;
}

.page-preorder .project-gallery {
    display: flex;
    overflow-x: auto;
    scroll-snap-type: x mandatory;
    scroll-behavior: smooth;
    gap: var(--space-md);
    padding: var(--space-md);
    height: 300px;
    align-items: center;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.page-preorder .project-gallery::-webkit-scrollbar {
    display: none;
}

.page-preorder .project-gallery .gallery-image {
    height: 100%;
    width: auto;
    object-fit: contain;
    border-radius: var(--radius-md);
    scroll-snap-align: start;
    flex-shrink: 0;
    transition: opacity 0.3s ease;
}

/* --- 2. Items Selection Section Styles --- */
.page-preorder .items-container {
    padding: var(--space-xl);
    margin-bottom: var(--space-xl);
    background-color: var(--bg-white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
}

.page-preorder .section-title {
    font-size: var(--font-size-xxl);
    font-weight: 700;
    margin-bottom: var(--space-xl);
    color: var(--text-dark);
    padding-bottom: var(--space-md);
    border-bottom: 2px solid var(--border-light);
}

.page-preorder .items-list {
    display: flex;
    flex-direction: column;
    gap: var(--space-lg);
}

/* Item Card Styles */
.page-preorder .item-card {
    display: grid;
    grid-template-columns: 1fr;
    align-items: center;
    gap: var(--space-lg);
    background-color: var(--bg-white);
    border-radius: var(--radius-lg);
    padding: var(--space-lg);
    border: 1px solid var(--border-light);
    transition: all 0.2s ease;
}

.page-preorder .item-card:hover {
    border-color: var(--primary-lighter);
    box-shadow: var(--shadow-sm);
    transform: translateY(-1px);
}

@media (min-width: 600px) {
    .page-preorder .item-card {
        grid-template-columns: 2fr auto auto;
        gap: var(--space-xl);
    }
}

.page-preorder .item-info {
    display: flex;
    flex-direction: column;
    gap: var(--space-sm);
}

.page-preorder .item-name {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-dark);
    line-height: 1.4;
}

.page-preorder .item-description {
    color: var(--text-medium);
    font-size: var(--font-size-base);
    line-height: var(--line-height-relaxed);
}

.page-preorder .item-price {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--secondary-color);
    text-align: right;
    white-space: nowrap;
}

/* Quantity Control */
.page-preorder .quantity-control {
    display: flex;
    align-items: center;
    background-color: var(--bg-light);
    border-radius: var(--radius-md);
    border: 1px solid var(--border-light);
    padding: var(--space-xs);
    width: 120px;
    justify-self: end;
    transition: border-color 0.2s ease;
}

.page-preorder .quantity-control:hover {
    border-color: var(--primary-lighter);
}

@media (max-width: 599px) {
    .page-preorder .quantity-control {
        justify-self: start;
        margin-top: var(--space-sm);
    }
}

.page-preorder .quantity-btn {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: none;
    border: none;
    color: var(--text-medium);
    font-size: var(--font-size-lg);
    cursor: pointer;
    border-radius: var(--radius-sm);
    transition: all 0.2s ease;
}

.page-preorder .quantity-btn:hover:not(:disabled) {
    background-color: var(--bg-medium);
    color: var(--text-dark);
}

.page-preorder .quantity-btn:disabled {
    color: var(--text-light);
    cursor: not-allowed;
}

.page-preorder .quantity-input {
    width: 48px;
    height: 32px;
    border: none;
    border-left: 1px solid var(--border-light);
    border-right: 1px solid var(--border-light);
    text-align: center;
    font-size: var(--font-size-base);
    color: var(--text-dark);
    font-weight: 500;
    background: transparent;
    padding: 0 var(--space-xs);
    appearance: textfield;
    -moz-appearance: textfield;
}

.page-preorder .quantity-input::-webkit-outer-spin-button,
.page-preorder .quantity-input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

/* --- 3. Order Summary & Submission Section Styles --- */
.page-preorder .order-summary {
    padding: var(--space-xl);
    background-color: var(--bg-white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
}

.page-preorder .summary-section {
    margin-bottom: var(--space-xl);
}

.page-preorder .summary-section:last-child {
    margin-bottom: 0;
}

/* Selected Items Summary */
.page-preorder .selected-items-summary .section-title {
    margin-bottom: var(--space-lg);
}

.page-preorder .cart-items {
    margin-top: var(--space-lg);
}

.page-preorder .cart-empty-message {
    text-align: center;
    color: var(--text-light);
    padding: var(--space-xl);
    font-style: italic;
    border: 2px dashed var(--border-light);
    border-radius: var(--radius-lg);
    background-color: var(--bg-lighter);
}

.page-preorder .cart-item {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: center;
    padding: var(--space-md) var(--space-lg);
    gap: var(--space-md);
    border-bottom: 1px solid var(--border-light);
    transition: background-color 0.2s ease;
}

.page-preorder .cart-item:hover {
    background-color: var(--bg-lighter);
}

.page-preorder .cart-item:last-child {
    border-bottom: none;
}

.page-preorder .cart-item-info {
    display: flex;
    flex-direction: column;
    gap: var(--space-xs);
    flex-grow: 1;
}

.page-preorder .cart-item-name {
    font-weight: 500;
    color: var(--text-dark);
    font-size: var(--font-size-base);
}

.page-preorder .item-quantity {
    font-size: var(--font-size-sm);
    color: var(--text-medium);
}

.page-preorder .cart-item-actions {
    display: flex;
    align-items: center;
    gap: var(--space-md);
    flex-shrink: 0;
}

.page-preorder .remove-item-btn {
    border-color: var(--danger-color) !important;
    color: var(--danger-color) !important;
    padding: var(--space-xs) var(--space-md);
    transition: all 0.2s ease;
}

.page-preorder .remove-item-btn:hover {
    background-color: var(--danger-lighter) !important;
    transform: translateY(-1px);
}

.page-preorder .cart-item-price {
    font-weight: 600;
    color: var(--text-dark);
    white-space: nowrap;
    min-width: 80px;
    text-align: right;
}

/* Calculation Summary */
.page-preorder .calculation-summary .section-title {
    margin-bottom: var(--space-lg);
}

.page-preorder .discount-info {
    margin-top: calc(-1 * var(--space-lg) + var(--space-sm));
    margin-bottom: var(--space-md);
    font-size: var(--font-size-sm);
    color: var(--primary-color);
    text-align: right;
    font-weight: 500;
    min-height: 1.4em;
}

.page-preorder .total-section {
    border-top: 2px solid var(--border-light);
    padding-top: var(--space-lg);
    margin-top: 0;
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: var(--space-md);
}

.page-preorder .total-row {
    display: flex;
    justify-content: space-between;
    width: 100%;
    max-width: 350px;
    font-size: var(--font-size-base);
    padding: var(--space-xs) 0;
    transition: background-color 0.2s ease;
}

.page-preorder .total-row:hover {
    background-color: var(--bg-lighter);
}

.page-preorder .total-label {
    color: var(--text-medium);
}

.page-preorder .total-amount {
    font-weight: 500;
    min-width: 100px;
    text-align: right;
}

.page-preorder .discount-row .total-label,
.page-preorder .discount-row .total-amount {
    color: var(--danger-color);
}

.page-preorder .final-total-row {
    font-size: var(--font-size-xl);
    font-weight: 700;
    margin-top: var(--space-md);
    border-top: 2px solid var(--border-light);
    padding-top: var(--space-md);
    background-color: var(--bg-lighter);
    padding: var(--space-md);
    border-radius: var(--radius-md);
}

.page-preorder .final-total-row .total-label,
.page-preorder .final-total-row .total-amount {
    color: var(--text-dark);
}

/* Order Submission */
.page-preorder .order-submission .section-title {
    margin-bottom: var(--space-lg);
}

.page-preorder .form-group {
    margin-bottom: var(--space-lg);
}

.page-preorder .form-actions {
    display: flex;
    justify-content: flex-end;
    margin-top: var(--space-xl);
    padding-top: var(--space-lg);
    border-top: 1px solid var(--border-light);
}

.page-preorder .submit-btn {
    display: inline-flex;
    align-items: center;
    gap: var(--space-sm);
    padding: var(--space-md) var(--space-xl);
    font-size: var(--font-size-lg);
    font-weight: 600;
    transition: all 0.3s ease;
}

.page-preorder .submit-btn svg {
    width: 24px;
    height: 24px;
}

.page-preorder .submit-btn:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.page-preorder .submit-btn:disabled {
    background-color: var(--text-light);
    cursor: not-allowed;
    box-shadow: none;
    transform: none;
}

.page-preorder .submission-message {
    font-size: var(--font-size-base);
    font-weight: 500;
    padding: var(--space-sm) var(--space-md);
    border-radius: var(--radius-md);
    margin-top: var(--space-md);
}

.page-preorder .submission-message.success {
    color: var(--success-dark);
    background-color: var(--success-lighter);
}

.page-preorder .submission-message.error {
    color: var(--danger-dark);
    background-color: var(--danger-lighter);
}

/* Responsive Adjustments */
@media (max-width: 599px) {
    .page-preorder .submit-btn {
        width: 100%;
        justify-content: center;
    }
    
    .page-preorder .total-row {
        max-width: none;
    }
    
    .page-preorder .project-highlights {
        gap: var(--space-lg);
    }
    
    .page-preorder .highlight-icon {
        width: 32px;
        height: 32px;
        font-size: 20px;
    }
    
    .page-preorder .highlight-text {
        font-size: var(--font-size-sm);
    }
    
    .page-preorder .project-title {
        font-size: var(--font-size-xxl);
    }
    
    .page-preorder .section-title {
        font-size: var(--font-size-xl);
    }
}