/**
 * DOM 工具函數
 * 封裝常用的 DOM 操作
 */

/**
 * 安全地獲取 DOM 元素
 * @param {string} selector - CSS 選擇器
 * @param {Element} parent - 父元素（可選）
 * @returns {Element|null} DOM 元素
 */
export function $(selector, parent = document) {
  return parent.querySelector(selector);
}

/**
 * 獲取多個 DOM 元素
 * @param {string} selector - CSS 選擇器
 * @param {Element} parent - 父元素（可選）
 * @returns {NodeList} DOM 元素列表
 */
export function $$(selector, parent = document) {
  return parent.querySelectorAll(selector);
}

/**
 * 創建 DOM 元素
 * @param {string} tagName - 標籤名稱
 * @param {Object} attributes - 屬性對象
 * @param {string|Element|Array} children - 子元素
 * @returns {Element} 創建的 DOM 元素
 */
export function createElement(tagName, attributes = {}, children = null) {
  const element = document.createElement(tagName);

  // 設置屬性
  Object.entries(attributes).forEach(([key, value]) => {
    if (key === 'className') {
      element.className = value;
    } else if (key === 'dataset') {
      Object.entries(value).forEach(([dataKey, dataValue]) => {
        element.dataset[dataKey] = dataValue;
      });
    } else if (key.startsWith('on') && typeof value === 'function') {
      // 事件監聽器
      element.addEventListener(key.slice(2).toLowerCase(), value);
    } else {
      element.setAttribute(key, value);
    }
  });

  // 添加子元素
  if (children) {
    if (typeof children === 'string') {
      element.textContent = children;
    } else if (children instanceof Element) {
      element.appendChild(children);
    } else if (Array.isArray(children)) {
      children.forEach(child => {
        if (typeof child === 'string') {
          element.appendChild(document.createTextNode(child));
        } else if (child instanceof Element) {
          element.appendChild(child);
        }
      });
    }
  }

  return element;
}

/**
 * 顯示元素
 * @param {Element} element - DOM 元素
 * @param {string} display - 顯示類型（可選）
 */
export function show(element, display = 'block') {
  if (element) {
    element.style.display = display;
  }
}

/**
 * 隱藏元素
 * @param {Element} element - DOM 元素
 */
export function hide(element) {
  if (element) {
    element.style.display = 'none';
  }
}

/**
 * 切換元素顯示/隱藏
 * @param {Element} element - DOM 元素
 * @param {string} display - 顯示類型（可選）
 */
export function toggle(element, display = 'block') {
  if (element) {
    if (element.style.display === 'none') {
      show(element, display);
    } else {
      hide(element);
    }
  }
}

/**
 * 添加 CSS 類
 * @param {Element} element - DOM 元素
 * @param {...string} classNames - 類名
 */
export function addClass(element, ...classNames) {
  if (element) {
    element.classList.add(...classNames);
  }
}

/**
 * 移除 CSS 類
 * @param {Element} element - DOM 元素
 * @param {...string} classNames - 類名
 */
export function removeClass(element, ...classNames) {
  if (element) {
    element.classList.remove(...classNames);
  }
}

/**
 * 切換 CSS 類
 * @param {Element} element - DOM 元素
 * @param {string} className - 類名
 * @param {boolean} force - 強制添加/移除（可選）
 */
export function toggleClass(element, className, force) {
  if (element) {
    return element.classList.toggle(className, force);
  }
  return false;
}

/**
 * 檢查元素是否包含指定類
 * @param {Element} element - DOM 元素
 * @param {string} className - 類名
 * @returns {boolean} 是否包含類
 */
export function hasClass(element, className) {
  return element ? element.classList.contains(className) : false;
}

/**
 * 設置元素內容
 * @param {Element} element - DOM 元素
 * @param {string} content - 內容
 * @param {boolean} isHTML - 是否為 HTML（預設為 false）
 */
export function setContent(element, content, isHTML = false) {
  if (element) {
    if (isHTML) {
      element.innerHTML = content;
    } else {
      element.textContent = content;
    }
  }
}

/**
 * 獲取元素內容
 * @param {Element} element - DOM 元素
 * @param {boolean} isHTML - 是否獲取 HTML（預設為 false）
 * @returns {string} 元素內容
 */
export function getContent(element, isHTML = false) {
  if (!element) return '';
  return isHTML ? element.innerHTML : element.textContent;
}

/**
 * 清空元素內容
 * @param {Element} element - DOM 元素
 */
export function clearContent(element) {
  if (element) {
    element.innerHTML = '';
  }
}

/**
 * 設置元素屬性
 * @param {Element} element - DOM 元素
 * @param {string|Object} attr - 屬性名或屬性對象
 * @param {string} value - 屬性值（當 attr 為字串時）
 */
export function setAttr(element, attr, value) {
  if (!element) return;

  if (typeof attr === 'object') {
    Object.entries(attr).forEach(([key, val]) => {
      element.setAttribute(key, val);
    });
  } else {
    element.setAttribute(attr, value);
  }
}

/**
 * 獲取元素屬性
 * @param {Element} element - DOM 元素
 * @param {string} attr - 屬性名
 * @returns {string|null} 屬性值
 */
export function getAttr(element, attr) {
  return element ? element.getAttribute(attr) : null;
}

/**
 * 移除元素屬性
 * @param {Element} element - DOM 元素
 * @param {...string} attrs - 屬性名
 */
export function removeAttr(element, ...attrs) {
  if (element) {
    attrs.forEach(attr => element.removeAttribute(attr));
  }
}

/**
 * 添加事件監聽器
 * @param {Element} element - DOM 元素
 * @param {string} event - 事件名稱
 * @param {Function} handler - 事件處理函數
 * @param {Object} options - 事件選項（可選）
 */
export function on(element, event, handler, options) {
  if (element && typeof handler === 'function') {
    element.addEventListener(event, handler, options);
  }
}

/**
 * 移除事件監聽器
 * @param {Element} element - DOM 元素
 * @param {string} event - 事件名稱
 * @param {Function} handler - 事件處理函數
 * @param {Object} options - 事件選項（可選）
 */
export function off(element, event, handler, options) {
  if (element && typeof handler === 'function') {
    element.removeEventListener(event, handler, options);
  }
}

/**
 * 一次性事件監聽器
 * @param {Element} element - DOM 元素
 * @param {string} event - 事件名稱
 * @param {Function} handler - 事件處理函數
 */
export function once(element, event, handler) {
  if (element && typeof handler === 'function') {
    element.addEventListener(event, handler, { once: true });
  }
}

/**
 * 等待 DOM 載入完成
 * @param {Function} callback - 回調函數
 */
export function ready(callback) {
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', callback);
  } else {
    callback();
  }
}

/**
 * 平滑滾動到元素
 * @param {Element} element - 目標元素
 * @param {Object} options - 滾動選項
 */
export function scrollToElement(element, options = {}) {
  if (element) {
    element.scrollIntoView({
      behavior: 'smooth',
      block: 'start',
      inline: 'nearest',
      ...options
    });
  }
}

/**
 * 獲取元素的位置和尺寸
 * @param {Element} element - DOM 元素
 * @returns {Object} 位置和尺寸資訊
 */
export function getElementRect(element) {
  if (!element) return null;
  return element.getBoundingClientRect();
}

/**
 * 檢查元素是否在視窗內
 * @param {Element} element - DOM 元素
 * @returns {boolean} 是否在視窗內
 */
export function isElementInViewport(element) {
  if (!element) return false;

  const rect = element.getBoundingClientRect();
  return (
    rect.top >= 0 &&
    rect.left >= 0 &&
    rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
    rect.right <= (window.innerWidth || document.documentElement.clientWidth)
  );
}

/**
 * 防抖函數
 * @param {Function} func - 要防抖的函數
 * @param {number} wait - 等待時間（毫秒）
 * @returns {Function} 防抖後的函數
 */
export function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

/**
 * 節流函數
 * @param {Function} func - 要節流的函數
 * @param {number} limit - 限制時間（毫秒）
 * @returns {Function} 節流後的函數
 */
export function throttle(func, limit) {
  let inThrottle;
  return function executedFunction(...args) {
    if (!inThrottle) {
      func.apply(this, args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
}

/**
 * 顯示 Toast 通知
 * @param {string} message - 通知訊息
 * @param {string} type - 通知類型 ('success', 'error', 'warning', 'info')
 * @param {number} duration - 顯示時間（毫秒）
 */
export function showToast(message, type = 'info', duration = 3000) {
  // 創建 toast 容器（如果不存在）
  let toastContainer = document.getElementById('toast-container');
  if (!toastContainer) {
    toastContainer = createElement('div', {
      id: 'toast-container',
      style: `
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 10000;
        display: flex;
        flex-direction: column;
        gap: 10px;
      `
    });
    document.body.appendChild(toastContainer);
  }

  // 創建 toast 元素
  const toast = createElement('div', {
    className: `toast toast-${type}`,
    style: `
      padding: 12px 16px;
      border-radius: 8px;
      color: white;
      font-size: 14px;
      font-weight: 500;
      max-width: 300px;
      word-wrap: break-word;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      transform: translateX(100%);
      transition: transform 0.3s ease;
      background-color: ${getToastColor(type)};
    `
  }, message);

  toastContainer.appendChild(toast);

  // 觸發動畫
  setTimeout(() => {
    toast.style.transform = 'translateX(0)';
  }, 10);

  // 自動移除
  setTimeout(() => {
    toast.style.transform = 'translateX(100%)';
    setTimeout(() => {
      if (toast.parentNode) {
        toast.parentNode.removeChild(toast);
      }
    }, 300);
  }, duration);
}

/**
 * 獲取 Toast 顏色
 * @param {string} type - 通知類型
 * @returns {string} 顏色值
 */
function getToastColor(type) {
  const colors = {
    success: '#57AC5A',
    error: '#e74c3c',
    warning: '#f39c12',
    info: '#3498db'
  };
  return colors[type] || colors.info;
}

/**
 * 顯示確認對話框
 * @param {string} message - 確認訊息
 * @param {string} title - 對話框標題
 * @returns {Promise<boolean>} 用戶選擇結果
 */
export function showConfirm(message, title = '確認') {
  return new Promise((resolve) => {
    // 創建遮罩層
    const overlay = createElement('div', {
      style: `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 10001;
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0;
        transition: opacity 0.3s ease;
      `
    });

    // 創建對話框
    const dialog = createElement('div', {
      style: `
        background: white;
        border-radius: 12px;
        padding: 24px;
        max-width: 400px;
        width: 90%;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
        transform: scale(0.9);
        transition: transform 0.3s ease;
      `
    });

    // 標題
    const titleElement = createElement('h3', {
      style: `
        margin: 0 0 16px 0;
        font-size: 18px;
        font-weight: 600;
        color: #333;
      `
    }, title);

    // 訊息
    const messageElement = createElement('p', {
      style: `
        margin: 0 0 24px 0;
        font-size: 14px;
        line-height: 1.5;
        color: #666;
        white-space: pre-line;
      `
    }, message);

    // 按鈕容器
    const buttonContainer = createElement('div', {
      style: `
        display: flex;
        gap: 12px;
        justify-content: flex-end;
      `
    });

    // 取消按鈕
    const cancelButton = createElement('button', {
      style: `
        padding: 8px 16px;
        border: 1px solid #ddd;
        border-radius: 6px;
        background: white;
        color: #666;
        font-size: 14px;
        cursor: pointer;
        transition: all 0.2s ease;
      `,
      onclick: () => {
        closeDialog(false);
      }
    }, '取消');

    // 確認按鈕
    const confirmButton = createElement('button', {
      style: `
        padding: 8px 16px;
        border: none;
        border-radius: 6px;
        background: #e74c3c;
        color: white;
        font-size: 14px;
        cursor: pointer;
        transition: all 0.2s ease;
      `,
      onclick: () => {
        closeDialog(true);
      }
    }, '確認');

    // 組裝對話框
    buttonContainer.appendChild(cancelButton);
    buttonContainer.appendChild(confirmButton);
    dialog.appendChild(titleElement);
    dialog.appendChild(messageElement);
    dialog.appendChild(buttonContainer);
    overlay.appendChild(dialog);
    document.body.appendChild(overlay);

    // 顯示動畫
    setTimeout(() => {
      overlay.style.opacity = '1';
      dialog.style.transform = 'scale(1)';
    }, 10);

    // 關閉對話框
    function closeDialog(result) {
      overlay.style.opacity = '0';
      dialog.style.transform = 'scale(0.9)';
      setTimeout(() => {
        if (overlay.parentNode) {
          overlay.parentNode.removeChild(overlay);
        }
        resolve(result);
      }, 300);
    }

    // ESC 鍵關閉
    function handleKeydown(e) {
      if (e.key === 'Escape') {
        closeDialog(false);
        document.removeEventListener('keydown', handleKeydown);
      }
    }
    document.addEventListener('keydown', handleKeydown);

    // 點擊遮罩關閉
    overlay.addEventListener('click', (e) => {
      if (e.target === overlay) {
        closeDialog(false);
      }
    });
  });
}
