<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>小森活 - 預購專案</title> <!-- Updated Title -->
    <link rel="stylesheet" href="../shared-styles.css"> <!-- Shared Styles -->
    <link rel="stylesheet" href="preorder.css">       <!-- Styles for this page -->
    <link rel="stylesheet" href="nav-icons.css">      <!-- Navigation Icons Styles -->
</head>
<body class="page-preorder"> <!-- Specific page class -->

    <!-- 統一的頁眉 -->
    <header class="app-header">
        <a href="index.html" class="logo">
            <img src="forest-life-logo.png" alt="小森活 Logo" class="logo-img" />
            <span class="logo-text">小森活</span>
        </a>
        <div class="nav-right">
            <!-- Cart icon might still be useful as a quick indicator, but main actions are on page -->
            <a href="#orderSummarySection" class="cart-btn" aria-label="查看訂單摘要"> <!-- Link to summary section -->
                <span class="nav-icon cart-icon"></span> <!-- Cart Icon -->
                <div class="cart-badge" id="headerCartBadge">0</div> <!-- Needs JS update -->
            </a>
            <a href="profile.html" class="user-control" aria-label="我的帳戶"> <!-- Link to profile -->
                <div class="user-avatar">
                    <!-- <img src="/path/to/avatar.jpg" alt="用戶頭像"> -->
                    <span>U</span> <!-- Fallback Initial -->
                </div>
            </a>
        </div>
    </header>

    <!-- 主要內容區域 -->
    <main class="main-content">
        <div class="container">

            <!-- 1. 專案詳情區 -->
            <section class="project-details card" id="projectDetailsSection">
                <h1 class="project-title" id="projectTitle">[專案名稱載入中...]</h1>

                <div class="project-highlights" id="projectHighlights">
                    <div class="highlight-item">
                        <div class="highlight-icon">✅</div>
                        <span class="highlight-text">品質保證</span>
                    </div>
                    <div class="highlight-item">
                        <div class="highlight-icon">🏡</div>
                        <span class="highlight-text">在地小農</span>
                    </div>
                    <div class="highlight-item">
                        <div class="highlight-icon">🚚</div>
                        <span class="highlight-text">安心配送</span>
                    </div>
                </div>

                <div class="project-meta">
                    <span class="status project-status status-pending" id="projectStatus">[狀態]</span>
                    <span class="project-deadline" id="projectDeadline">截止日期：[日期]</span>
                </div>

                <p class="project-description" id="projectDescription">
                    [專案描述載入中...]
                </p>

                <div class="card-image-gallery-container project-gallery-container" id="projectGalleryContainer">
                    <div class="card-image-gallery project-gallery" id="projectGallery">
                        <!-- Images loaded by JS -->
                        <div class="gallery-image-placeholder"></div>
                    </div>
                </div>
            </section>

            <!-- 2. 商品選擇區 -->
            <section class="items-container card" id="itemsSelectionSection">
                <h2 class="section-title">選擇商品</h2>
                <div class="items-list" id="itemsList">
                    <!-- Items loaded by JS based on the project -->
                    <div class="item-card-placeholder">[商品列表載入中...]</div>
                    <!-- Example Structure (repeated by JS) -->
                    <!--
                    <div class="item-card" data-item-id="item-uuid-1">
                        <div class="item-info">
                            <h3 class="item-name">[商品名稱]</h3>
                            <p class="item-description">[商品描述]</p>
                        </div>
                        <div class="item-price">$[價格]</div>
                        <div class="quantity-control">
                            <button class="quantity-btn decrease-qty" aria-label="減少數量">-</button>
                            <input type="number" class="quantity-input" value="0" min="0" max="10" aria-label="數量" data-item-price="[價格]">
                            <button class="quantity-btn increase-qty" aria-label="增加數量">+</button>
                        </div>
                    </div>
                    -->
                </div>
            </section>

            <!-- 3. 訂單摘要與提交區 -->
            <!-- This section acts as the 'cart' and 'checkout' combined -->
            <section class="order-summary card" id="orderSummarySection">
                <!-- Section for displaying selected items -->
                <div class="summary-section selected-items-summary">
                    <h2 class="section-title">訂單內容</h2>
                    <div class="cart-items" id="cartItemsList">
                         <p class="cart-empty-message">請從上方選擇商品</p>
                         <!-- Selected items dynamically added here by JS -->
                         <!-- Example Structure (repeated by JS) -->
                         <!--
                         <div class="cart-item" data-summary-item-id="item-uuid-1">
                            <div class="cart-item-info">
                               <div class="cart-item-name">[商品名稱]</div>
                               <div class="item-quantity">(x[數量])</div>
                            </div>
                            <div class="cart-item-actions">
                               <div class="cart-item-price">$[小計]</div>
                               <button class="btn btn-outline btn-sm remove-item-btn">移除</button>
                            </div>
                        </div>
                        -->
                    </div>
                </div>

                <!-- Section for totals and discount -->
                <div class="summary-section calculation-summary">
                     <h2 class="section-title">金額計算</h2>
                     <div class="discount-info" id="discountInfo">
                        <!-- Discount message loaded by JS -->
                     </div>
                     <div class="total-section">
                        <div class="total-row">
                            <div class="total-label">商品小計</div>
                            <div class="total-amount" id="subtotalAmount">$0</div>
                        </div>
                         <div class="total-row discount-row" id="discountAmountRow">
                            <div class="total-label">數量折扣</div>
                            <div class="total-amount" id="discountAmountValue">-$0</div>
                        </div>
                         <div class="total-row final-total-row">
                            <div class="total-label">訂單總金額</div>
                            <div class="total-amount" id="finalTotalAmount">$0</div>
                        </div>
                    </div>
                </div>

                <!-- Section for notes and submission -->
                <div class="summary-section order-submission">
                    <h2 class="section-title">訂單資訊</h2>
                    <div class="form-group">
                       <label for="remarks" class="form-label">備註 (選填)</label>
                       <textarea id="remarks" class="form-textarea" placeholder="請填寫備註事項或特殊需求..."></textarea>
                    </div>
                    <div class="form-group">
                        <label for="pickupDate" class="form-label">預計取貨日期 (選填)</label>
                        <input type="date" id="pickupDate" class="form-input">
                    </div>
                    <div class="form-actions">
                        <button class="btn btn-primary btn-lg submit-btn" id="submitOrderBtn" disabled> <!-- Disabled initially -->
                            <span class="nav-icon check-circle-icon"></span>
                            確認送出訂單
                        </button>
                    </div>
                     <div id="submissionMessage" class="submission-message" style="margin-top: var(--space-md); text-align: right;"></div> <!-- For success/error messages -->
                </div>
            </section>

        </div> <!-- End .container -->
    </main>

    <!-- 統一的底部導航 -->
    <nav class="bottom-nav">
        <a href="index.html" class="nav-item">
            <span class="nav-icon home-icon"></span>
            <span class="nav-label">首頁</span>
        </a>
        <a href="preorder.html" class="nav-item active">
            <span class="nav-icon cart-icon"></span>
            <span class="nav-label">預購</span>
        </a>
        <a href="order-history.html" class="nav-item">
            <span class="nav-icon order-icon"></span>
            <span class="nav-label">我的訂單</span>
        </a>
    </nav>

    <!-- LIFF SDK -->
    <script charset="utf-8" src="https://static.line-scdn.net/liff/edge/2/sdk.js"></script>

    <!-- 主要初始化腳本 -->
    <script type="module" src="../js/main.js"></script>

    <!-- 預購頁面特定腳本 -->
    <script type="module" src="../js/pages/user/preorder.js"></script>

    <!-- 暫時性管理員入口 - 開發用，後續需移除 -->
    <div id="devAdminEntry" style="
      position: fixed;
      bottom: 20px;
      right: 20px;
      z-index: 9999;
      background: linear-gradient(45deg, #FF6B6B, #FF8E53);
      color: white;
      padding: 12px;
      border-radius: 50%;
      box-shadow: 0 4px 12px rgba(255, 107, 107, 0.3);
      cursor: pointer;
      transition: all 0.3s ease;
      width: 56px;
      height: 56px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 20px;
      text-decoration: none;
    "
    onmouseover="this.style.transform='scale(1.1)'"
    onmouseout="this.style.transform='scale(1)'"
    onclick="window.open('../admin/dashboard.html', '_blank')"
    title="管理員入口 (開發用)">
      🔐
    </div>
</body>
</html>