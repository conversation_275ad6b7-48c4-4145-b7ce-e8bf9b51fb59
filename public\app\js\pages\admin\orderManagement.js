/**
 * 訂單管理頁面邏輯
 * 處理訂單列表顯示、搜尋、篩選、批量操作等功能
 */

import {
  fetchAllOrders,
  fetchOrderDetails,
  updateOrderStatus,
  batchUpdateOrderStatus,
  deleteOrders,
  fetchAllProjects,
  initializeSupabase
} from '../../services/supabaseService.js';
import { formatCurrency, formatDate, formatRelativeTime } from '../../utils/dateUtils.js';
import { showToast, showConfirm } from '../../utils/domUtils.js';

class OrderManagement {
  constructor() {
    this.currentPage = 1;
    this.pageSize = 20;
    this.totalCount = 0;
    this.selectedOrders = new Set();
    this.filters = {};
    this.projects = [];

    this.init();
  }

  async init() {
    try {
      // 初始化 Supabase
      initializeSupabase();

      await this.loadProjects();
      await this.loadOrders();
      this.setupEventListeners();
      this.setupFilterForm();
    } catch (error) {
      console.error('Error initializing order management:', error);
      showToast('初始化失敗：' + error.message, 'error');
    }
  }

  /**
   * 載入專案列表用於篩選下拉選單
   */
  async loadProjects() {
    try {
      this.projects = await fetchAllProjects();
      this.populateProjectFilter();
    } catch (error) {
      console.error('Error loading projects:', error);
    }
  }

  /**
   * 填充專案篩選下拉選單
   */
  populateProjectFilter() {
    const projectSelect = document.querySelector('.filter-select[data-filter="project"]');
    if (!projectSelect) return;

    // 清空現有選項（保留第一個"全部專案"選項）
    projectSelect.innerHTML = '<option value="">全部專案</option>';

    // 添加專案選項
    this.projects.forEach(project => {
      const option = document.createElement('option');
      option.value = project.id;
      option.textContent = `${project.name} (${project.project_id_display})`;
      projectSelect.appendChild(option);
    });
  }

  /**
   * 載入訂單列表
   */
  async loadOrders() {
    try {
      this.showLoading(true);

      const result = await fetchAllOrders(this.filters, {
        page: this.currentPage,
        limit: this.pageSize
      });

      this.totalCount = result.totalCount;
      this.renderOrders(result.orders);
      this.renderPagination();
      this.updateBatchActions();

    } catch (error) {
      console.error('Error loading orders:', error);
      showToast('載入訂單失敗：' + error.message, 'error');
    } finally {
      this.showLoading(false);
    }
  }

  /**
   * 渲染訂單列表
   */
  renderOrders(orders) {
    const orderList = document.querySelector('.order-list');
    if (!orderList) return;

    // 保留表頭，清空訂單行
    const existingRows = orderList.querySelectorAll('.order-row');
    existingRows.forEach(row => row.remove());

    if (orders.length === 0) {
      this.renderEmptyState();
      return;
    }

    orders.forEach(order => {
      const orderRow = this.createOrderRow(order);
      orderList.appendChild(orderRow);
    });
  }

  /**
   * 創建訂單行元素
   */
  createOrderRow(order) {
    const row = document.createElement('div');
    row.className = 'order-row';
    row.dataset.orderId = order.id;

    const totalDiscount = (order.discount_amount || 0) + (order.manual_discount_amount || 0);

    row.innerHTML = `
      <div>
        <input type="checkbox" class="list-checkbox order-checkbox" value="${order.id}">
      </div>
      <div class="order-id" data-label="訂單編號">${order.order_number}</div>
      <div class="order-customer" data-label="客戶">
        <div class="customer-name">${order.user_name}</div>
        <div class="customer-id">ID: ${order.user_id}</div>
      </div>
      <div class="order-amount" data-label="金額">${formatCurrency(order.final_amount)}</div>
      <div class="order-discount" data-label="折扣">-${formatCurrency(totalDiscount)}</div>
      <div class="order-status-container" data-label="狀態">
        <span class="order-status ${this.getStatusClass(order.status)}">${this.getStatusText(order.status)}</span>
      </div>
      <div class="order-date" data-label="日期">${formatDate(order.order_date)}</div>
      <div class="order-actions">
        <button class="action-btn view-order-btn" title="查看詳情" data-order-id="${order.id}">
          <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
            <circle cx="12" cy="12" r="3"></circle>
          </svg>
        </button>
      </div>
    `;

    return row;
  }

  /**
   * 渲染空狀態
   */
  renderEmptyState() {
    const orderList = document.querySelector('.order-list');
    if (!orderList) return;

    const emptyRow = document.createElement('div');
    emptyRow.className = 'order-row';
    emptyRow.innerHTML = `
      <div style="grid-column: 1 / -1; text-align: center; padding: 2rem; color: #666;">
        <p>沒有找到符合條件的訂單</p>
      </div>
    `;
    orderList.appendChild(emptyRow);
  }

  /**
   * 獲取狀態對應的 CSS 類別
   */
  getStatusClass(status) {
    const statusMap = {
      'Pending': 'status-pending',
      'Confirmed': 'status-confirmed',
      'Completed': 'status-completed',
      'Cancelled': 'status-cancelled'
    };
    return statusMap[status] || 'status-pending';
  }

  /**
   * 獲取狀態顯示文字
   */
  getStatusText(status) {
    const statusMap = {
      'Pending': '待處理',
      'Confirmed': '已確認',
      'Completed': '已完成',
      'Cancelled': '已取消'
    };
    return statusMap[status] || status;
  }

  /**
   * 渲染分頁
   */
  renderPagination() {
    const pagination = document.querySelector('.pagination');
    if (!pagination) return;

    const totalPages = Math.ceil(this.totalCount / this.pageSize);

    pagination.innerHTML = '';

    // 上一頁按鈕
    const prevBtn = document.createElement('button');
    prevBtn.className = 'page-btn';
    prevBtn.innerHTML = '&lt;';
    prevBtn.disabled = this.currentPage === 1;
    prevBtn.addEventListener('click', () => this.goToPage(this.currentPage - 1));
    pagination.appendChild(prevBtn);

    // 頁碼按鈕
    const startPage = Math.max(1, this.currentPage - 2);
    const endPage = Math.min(totalPages, this.currentPage + 2);

    for (let i = startPage; i <= endPage; i++) {
      const pageBtn = document.createElement('button');
      pageBtn.className = `page-btn ${i === this.currentPage ? 'active' : ''}`;
      pageBtn.textContent = i;
      pageBtn.addEventListener('click', () => this.goToPage(i));
      pagination.appendChild(pageBtn);
    }

    // 下一頁按鈕
    const nextBtn = document.createElement('button');
    nextBtn.className = 'page-btn';
    nextBtn.innerHTML = '&gt;';
    nextBtn.disabled = this.currentPage === totalPages;
    nextBtn.addEventListener('click', () => this.goToPage(this.currentPage + 1));
    pagination.appendChild(nextBtn);
  }

  /**
   * 跳轉到指定頁面
   */
  async goToPage(page) {
    if (page < 1 || page > Math.ceil(this.totalCount / this.pageSize)) return;

    this.currentPage = page;
    await this.loadOrders();
  }

  /**
   * 顯示/隱藏載入狀態
   */
  showLoading(show) {
    const orderList = document.querySelector('.order-list');
    if (!orderList) return;

    if (show) {
      orderList.style.opacity = '0.5';
      orderList.style.pointerEvents = 'none';
    } else {
      orderList.style.opacity = '1';
      orderList.style.pointerEvents = 'auto';
    }
  }

  /**
   * 設置事件監聽器
   */
  setupEventListeners() {
    // 全選/取消全選
    const selectAllCheckbox = document.querySelector('.list-header .list-checkbox');
    if (selectAllCheckbox) {
      selectAllCheckbox.addEventListener('change', (e) => {
        this.toggleSelectAll(e.target.checked);
      });
    }

    // 訂單選擇
    document.addEventListener('change', (e) => {
      if (e.target.classList.contains('order-checkbox')) {
        this.toggleOrderSelection(e.target.value, e.target.checked);
      }
    });

    // 查看訂單詳情
    document.addEventListener('click', (e) => {
      if (e.target.closest('.view-order-btn')) {
        const orderId = e.target.closest('.view-order-btn').dataset.orderId;
        this.viewOrderDetails(orderId);
      }
    });

    // 批量操作按鈕
    const batchUpdateBtn = document.querySelector('.batch-actions-container .btn-outline');
    if (batchUpdateBtn) {
      batchUpdateBtn.addEventListener('click', () => this.showBatchUpdateModal());
    }

    const batchDeleteBtn = document.querySelector('.batch-actions-container .btn-danger-outline');
    if (batchDeleteBtn) {
      batchDeleteBtn.addEventListener('click', () => this.batchDeleteOrders());
    }
  }

  /**
   * 設置篩選表單
   */
  setupFilterForm() {
    // 搜尋按鈕
    const searchBtn = document.querySelector('.filter-row .btn-primary');
    if (searchBtn) {
      searchBtn.addEventListener('click', () => this.applyFilters());
    }

    // 重置按鈕
    const resetBtn = document.querySelector('.filter-actions .btn-outline');
    if (resetBtn) {
      resetBtn.addEventListener('click', () => this.resetFilters());
    }

    // Enter 鍵搜尋
    const filterInputs = document.querySelectorAll('.filter-input');
    filterInputs.forEach(input => {
      input.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
          this.applyFilters();
        }
      });
    });
  }

  /**
   * 應用篩選條件
   */
  async applyFilters() {
    this.filters = {};

    // 訂單編號
    const orderNumber = document.querySelector('.filter-input[placeholder*="訂單編號"]')?.value.trim();
    if (orderNumber) {
      this.filters.orderNumber = orderNumber;
    }

    // 用戶名稱
    const userName = document.querySelector('.filter-input[placeholder*="用戶名稱"]')?.value.trim();
    if (userName) {
      this.filters.userName = userName;
    }

    // 專案
    const projectSelect = document.querySelector('.filter-select[data-filter="project"]');
    if (projectSelect?.value) {
      this.filters.projectId = projectSelect.value;
    }

    // 訂單狀態
    const statusSelect = document.querySelector('.filter-select[data-filter="status"]');
    if (statusSelect?.value) {
      this.filters.status = statusSelect.value;
    }

    // 日期範圍
    const startDate = document.querySelector('.filter-input[type="date"]:first-of-type')?.value;
    if (startDate) {
      this.filters.startDate = startDate;
    }

    const endDate = document.querySelector('.filter-input[type="date"]:last-of-type')?.value;
    if (endDate) {
      this.filters.endDate = endDate;
    }

    // 重置到第一頁並載入
    this.currentPage = 1;
    await this.loadOrders();
  }

  /**
   * 重置篩選條件
   */
  async resetFilters() {
    // 清空所有輸入框
    document.querySelectorAll('.filter-input').forEach(input => {
      input.value = '';
    });

    // 重置下拉選單
    document.querySelectorAll('.filter-select').forEach(select => {
      select.selectedIndex = 0;
    });

    // 清空篩選條件並重新載入
    this.filters = {};
    this.currentPage = 1;
    await this.loadOrders();
  }

  /**
   * 切換全選狀態
   */
  toggleSelectAll(checked) {
    const orderCheckboxes = document.querySelectorAll('.order-checkbox');
    orderCheckboxes.forEach(checkbox => {
      checkbox.checked = checked;
      this.toggleOrderSelection(checkbox.value, checked);
    });
  }

  /**
   * 切換訂單選擇狀態
   */
  toggleOrderSelection(orderId, selected) {
    if (selected) {
      this.selectedOrders.add(orderId);
    } else {
      this.selectedOrders.delete(orderId);
    }
    this.updateBatchActions();
  }

  /**
   * 更新批量操作按鈕狀態
   */
  updateBatchActions() {
    const batchUpdateBtn = document.querySelector('.batch-actions-container .btn-outline');
    const batchDeleteBtn = document.querySelector('.batch-actions-container .btn-danger-outline');

    const hasSelection = this.selectedOrders.size > 0;

    if (batchUpdateBtn) {
      batchUpdateBtn.disabled = !hasSelection;
      batchUpdateBtn.textContent = hasSelection
        ? `更新選取狀態 (${this.selectedOrders.size})`
        : '更新選取狀態';
    }

    if (batchDeleteBtn) {
      batchDeleteBtn.disabled = !hasSelection;
      batchDeleteBtn.textContent = hasSelection
        ? `刪除選取項目 (${this.selectedOrders.size})`
        : '刪除選取項目';
    }
  }

  /**
   * 查看訂單詳情
   */
  async viewOrderDetails(orderId) {
    try {
      const orderDetails = await fetchOrderDetails(orderId);
      this.showOrderDetailsModal(orderDetails);
    } catch (error) {
      console.error('Error fetching order details:', error);
      showToast('載入訂單詳情失敗：' + error.message, 'error');
    }
  }

  /**
   * 顯示訂單詳情模態框
   */
  showOrderDetailsModal(order) {
    // 這裡可以實現一個模態框來顯示訂單詳情
    // 暫時使用 alert 代替
    const itemsList = order.items.map(item =>
      `${item.item_name} x${item.quantity} = ${formatCurrency(item.subtotal)}`
    ).join('\n');

    const details = `
訂單編號：${order.order_number}
客戶：${order.user_name}
專案：${order.project_name}
總金額：${formatCurrency(order.total_amount)}
折扣：-${formatCurrency((order.discount_amount || 0) + (order.manual_discount_amount || 0))}
最終金額：${formatCurrency(order.final_amount)}
狀態：${this.getStatusText(order.status)}
訂單日期：${formatDate(order.order_date)}
取貨日期：${order.pickup_date ? formatDate(order.pickup_date) : '未設定'}
備註：${order.notes || '無'}
管理員備註：${order.admin_notes || '無'}

訂單項目：
${itemsList}
    `;

    alert(details);
  }

  /**
   * 顯示批量更新狀態模態框
   */
  async showBatchUpdateModal() {
    if (this.selectedOrders.size === 0) return;

    const newStatus = prompt('請選擇新狀態：\n1. Pending (待處理)\n2. Confirmed (已確認)\n3. Completed (已完成)\n4. Cancelled (已取消)\n\n請輸入數字 1-4：');

    const statusMap = {
      '1': 'Pending',
      '2': 'Confirmed',
      '3': 'Completed',
      '4': 'Cancelled'
    };

    const selectedStatus = statusMap[newStatus];
    if (!selectedStatus) {
      showToast('無效的狀態選擇', 'error');
      return;
    }

    const adminNotes = prompt('管理員備註（可選）：') || '';

    try {
      await batchUpdateOrderStatus(Array.from(this.selectedOrders), selectedStatus, adminNotes);
      showToast(`成功更新 ${this.selectedOrders.size} 個訂單狀態`, 'success');

      // 清空選擇並重新載入
      this.selectedOrders.clear();
      await this.loadOrders();
    } catch (error) {
      console.error('Error batch updating orders:', error);
      showToast('批量更新失敗：' + error.message, 'error');
    }
  }

  /**
   * 批量刪除訂單
   */
  async batchDeleteOrders() {
    if (this.selectedOrders.size === 0) return;

    const confirmed = await showConfirm(
      `確定要刪除選取的 ${this.selectedOrders.size} 個訂單嗎？\n\n此操作會將訂單狀態設為「已取消」，無法復原。`
    );

    if (!confirmed) return;

    try {
      await deleteOrders(Array.from(this.selectedOrders));
      showToast(`成功刪除 ${this.selectedOrders.size} 個訂單`, 'success');

      // 清空選擇並重新載入
      this.selectedOrders.clear();
      await this.loadOrders();
    } catch (error) {
      console.error('Error batch deleting orders:', error);
      showToast('批量刪除失敗：' + error.message, 'error');
    }
  }
}

// 初始化訂單管理
document.addEventListener('DOMContentLoaded', () => {
  new OrderManagement();
});
