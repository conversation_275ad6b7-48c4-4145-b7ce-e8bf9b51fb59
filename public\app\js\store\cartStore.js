/**
 * 購物車狀態管理
 * 使用 Nano Stores 管理購物車狀態，並持久化到 localStorage
 */

import { atom, computed } from 'nanostores';
import { persistentAtom } from '@nanostores/persistent';

// 購物車項目結構：
// {
//   [itemId]: {
//     id: string,
//     name: string,
//     price: number,
//     quantity: number,
//     projectId: string
//   }
// }

// 持久化的購物車狀態
export const cartItems = persistentAtom('cart-items', {}, {
  encode: JSON.stringify,
  decode: JSON.parse,
});

// 計算購物車總數量
export const totalCount = computed(cartItems, (items) => {
  return Object.values(items).reduce((total, item) => total + item.quantity, 0);
});

// 計算購物車小計
export const subtotal = computed(cartItems, (items) => {
  return Object.values(items).reduce((total, item) => total + (item.price * item.quantity), 0);
});

// 購物車操作函數

/**
 * 添加商品到購物車
 * @param {Object} item - 商品資訊
 * @param {string} item.id - 商品ID
 * @param {string} item.name - 商品名稱
 * @param {number} item.price - 商品價格
 * @param {string} item.projectId - 專案ID
 * @param {number} quantity - 數量
 */
export function addItem(item, quantity = 1) {
  const currentItems = cartItems.get();
  const existingItem = currentItems[item.id];
  
  if (existingItem) {
    // 如果商品已存在，增加數量
    updateItemQuantity(item.id, existingItem.quantity + quantity);
  } else {
    // 新增商品
    cartItems.set({
      ...currentItems,
      [item.id]: {
        id: item.id,
        name: item.name,
        price: item.price,
        quantity: quantity,
        projectId: item.projectId
      }
    });
  }
}

/**
 * 更新商品數量
 * @param {string} itemId - 商品ID
 * @param {number} quantity - 新數量
 */
export function updateItemQuantity(itemId, quantity) {
  const currentItems = cartItems.get();
  
  if (quantity <= 0) {
    // 如果數量為0或負數，移除商品
    removeItem(itemId);
  } else {
    // 更新數量
    cartItems.set({
      ...currentItems,
      [itemId]: {
        ...currentItems[itemId],
        quantity: quantity
      }
    });
  }
}

/**
 * 移除商品
 * @param {string} itemId - 商品ID
 */
export function removeItem(itemId) {
  const currentItems = cartItems.get();
  const newItems = { ...currentItems };
  delete newItems[itemId];
  cartItems.set(newItems);
}

/**
 * 清空購物車
 */
export function clearCart() {
  cartItems.set({});
}

/**
 * 獲取指定商品的數量
 * @param {string} itemId - 商品ID
 * @returns {number} 商品數量
 */
export function getItemQuantity(itemId) {
  const currentItems = cartItems.get();
  return currentItems[itemId]?.quantity || 0;
}

/**
 * 檢查購物車是否為空
 * @returns {boolean} 是否為空
 */
export function isEmpty() {
  return Object.keys(cartItems.get()).length === 0;
}

/**
 * 獲取購物車項目陣列
 * @returns {Array} 購物車項目陣列
 */
export function getCartItemsArray() {
  return Object.values(cartItems.get());
}
