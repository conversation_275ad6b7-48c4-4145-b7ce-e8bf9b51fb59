import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

// https://vite.dev/config/
export default defineConfig({
  plugins: [react()],
  // 配置 public 目錄下的 app 作為靜態資源
  publicDir: 'public',
  // 開發服務器配置
  server: {
    port: 3000,
    open: '/app/user/index.html', // 開發時自動打開真正的用戶首頁
  },
  // 構建配置
  build: {
    outDir: 'dist',
    // 確保 public/app 下的文件被正確複製
    copyPublicDir: true,
  },
})
