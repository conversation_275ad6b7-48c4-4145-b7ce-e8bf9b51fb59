<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>小森活後台 - 儀表板</title>
    <link rel="stylesheet" href="../shared-styles.css">
    <style>
        /* 後台特定樣式 */
        body {
            min-height: 100vh;
            display: flex;
        }

        /* 側邊導航 */
        .sidebar {
            width: 250px;
            background-color: var(--bg-white);
            box-shadow: var(--shadow-md);
            padding: var(--space-lg) 0;
            position: fixed;
            height: 100vh;
            overflow-y: auto;
            z-index: 10;
        }

        .logo-container {
            padding: 0 var(--space-lg);
            margin-bottom: var(--space-xl);
            display: flex;
            align-items: center;
        }

        .logo {
            width: 40px;
            margin-right: var(--space-sm);
        }

        .logo-text {
            font-size: 1.2rem;
            font-weight: 700;
            color: var(--primary-color);
        }

        .nav-title {
            padding: var(--space-sm) var(--space-lg);
            font-size: 0.75rem;
            font-weight: 600;
            color: var(--text-medium);
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .nav-item {
            display: flex;
            align-items: center;
            padding: var(--space-md) var(--space-lg);
            color: var(--text-medium);
            text-decoration: none;
            transition: all 0.2s ease;
            margin-bottom: var(--space-xs);
        }

        .nav-item.active {
            background-color: var(--primary-light);
            color: var(--primary-color);
            font-weight: 500;
            border-right: 3px solid var(--primary-color);
        }

        .nav-item:hover:not(.active) {
            background-color: rgba(0, 0, 0, 0.03);
        }

        .nav-icon {
            margin-right: var(--space-md);
            width: 20px;
            height: 20px;
        }

        .user-info {
            padding: 1.5rem;
            margin-top: auto;
            border-top: 1px solid #eee;
            font-size: 0.9rem;
        }

        .user-name {
            font-weight: 600;
            margin-bottom: 0.25rem;
        }

        .user-role {
            color: #666;
            font-size: 0.8rem;
        }

        /* 主内容 */
        .main-content {
            flex: 1;
            margin-left: 250px;
            padding: 2rem;
        }

        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
        }

        .page-title {
            font-size: 1.8rem;
            font-weight: 700;
            color: #333;
        }

        .stats-container {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background-color: white;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            padding: 1.5rem;
            transition: all 0.2s ease;
        }

        .stat-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 15px rgba(0, 0, 0, 0.08);
        }

        .stat-title {
            color: #666;
            font-size: 0.9rem;
            margin-bottom: 0.5rem;
        }

        .stat-value {
            font-size: 2rem;
            font-weight: 700;
            color: #333;
            margin-bottom: 0.5rem;
        }

        .stat-trend {
            display: flex;
            align-items: center;
            font-size: 0.8rem;
        }

        .trend-up {
            color: #57AC5A;
        }

        .trend-down {
            color: #e74c3c;
        }

        .trend-icon {
            margin-right: 0.25rem;
        }

        .chart-container {
            background-color: white;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            padding: 1.5rem;
            margin-bottom: 2rem;
        }

        .chart-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .chart-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #333;
        }

        .period-selector {
            display: flex;
            gap: 0.5rem;
        }

        /* Standardized period button style (like btn-outline btn-sm) */
        .period-btn {
            padding: 0.5rem 1rem; /* Match btn-sm padding */
            font-size: 0.9rem; /* Match btn-sm font-size */
            border-radius: 8px; /* Match standard radius */
            font-weight: 500; /* Match standard weight */
            cursor: pointer;
            transition: all 0.2s ease;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            background-color: transparent;
            color: #57AC5A; /* Match btn-outline color */
            border: 2px solid #57AC5A; /* Match btn-outline border */
        }

        .period-btn:hover:not(.active) {
             background-color: rgba(87, 172, 90, 0.05); /* Match btn-outline hover */
        }

        .period-btn.active {
            background-color: #57AC5A; /* Keep active state */
            border-color: #57AC5A;
            color: white;
            box-shadow: 0 2px 6px rgba(87, 172, 90, 0.2); /* Add shadow like btn-primary */
        }

        .chart-placeholder {
            height: 300px;
            background-color: #f9f9f9;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #888;
            font-size: 0.9rem;
        }

        .recent-activity {
            background-color: white;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            padding: 1.5rem;
        }

        .activity-list {
            margin-top: 1rem;
        }

        .activity-item {
            display: flex;
            padding: 1rem 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .activity-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: rgba(87, 172, 90, 0.1);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 1rem;
            color: #57AC5A;
        }

        .activity-content {
            flex: 1;
        }

        .activity-title {
            font-weight: 600;
            margin-bottom: 0.25rem;
        }

        .activity-time {
            font-size: 0.8rem;
            color: #888;
        }

        .quick-actions {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 1rem;
            margin-top: 2rem;
        }

        .action-card {
            background-color: white;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            padding: 1.5rem;
            text-align: center;
            transition: all 0.2s ease;
            cursor: pointer;
            text-decoration: none;
            color: inherit;
        }

        .action-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 15px rgba(0, 0, 0, 0.08);
        }

        .action-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background-color: rgba(87, 172, 90, 0.1);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            color: #57AC5A;
            font-size: 1.5rem;
        }

        .action-title {
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .action-desc {
            font-size: 0.85rem;
            color: #666;
        }

        /* 統計卡片樣式 */
        .stats-container {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background-color: white;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            padding: 1.5rem;
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .stat-icon {
            font-size: 2rem;
            width: 60px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: rgba(87, 172, 90, 0.1);
            border-radius: 50%;
        }

        .stat-content {
            flex: 1;
        }

        .stat-number {
            font-size: 1.8rem;
            font-weight: 700;
            color: #333;
            margin-bottom: 0.25rem;
        }

        .stat-label {
            font-size: 0.9rem;
            color: #666;
        }

        /* 響應式設計 */
        @media (max-width: 992px) {
            .sidebar {
                width: 200px;
            }

            .main-content {
                margin-left: 200px;
            }

            .quick-actions {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
                position: fixed;
                top: 0;
                left: 0;
                z-index: 100;
                width: 250px;
                background-color: white;
            }

            .sidebar.active {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
                padding: 1rem;
            }

            .stats-container {
                grid-template-columns: 1fr;
            }

            .quick-actions {
                grid-template-columns: 1fr;
            }

            .menu-toggle {
                display: block;
                position: fixed;
                top: 1rem;
                left: 1rem;
                z-index: 101;
                background-color: #57AC5A;
                color: white;
                border: none;
                border-radius: 4px;
                width: 40px;
                height: 40px;
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;
                box-shadow: 0 2px 8px rgba(87, 172, 90, 0.2);
            }
        }
    </style>
</head>
<body>
    <aside class="sidebar">
        <div class="logo-container">
            <img src="../user/forest-life-logo.png" alt="小森活" class="logo">
            <div class="logo-text">小森活管理系統</div>
        </div>

        <div class="nav-title">主選單</div>
        <a href="dashboard.html" class="nav-item active">
            <svg class="nav-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <rect x="3" y="3" width="7" height="9" />
                <rect x="14" y="3" width="7" height="5" />
                <rect x="14" y="12" width="7" height="9" />
                <rect x="3" y="16" width="7" height="5" />
            </svg>
            儀表板
        </a>
        <a href="management/projects.html" class="nav-item">
            <svg class="nav-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2" />
                <rect x="8" y="2" width="8" height="4" rx="1" ry="1" />
            </svg>
            專案管理
        </a>
        <a href="management/order-management.html" class="nav-item">
            <svg class="nav-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" />
                <path d="M14 2v6h6" />
                <path d="M16 13H8" />
                <path d="M16 17H8" />
                <path d="M10 9H8" />
            </svg>
            訂單管理
        </a>
        <a href="management/user-management.html" class="nav-item">
            <svg class="nav-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2" />
                <circle cx="12" cy="7" r="4" />
            </svg>
            用戶管理
        </a>

        <!-- 快速跳轉前台 -->
        <div style="margin-top: auto; padding-top: 2rem; border-top: 1px solid #e0e0e0;">
            <a href="../user/index.html" class="nav-item" style="color: #57AC5A; font-weight: 600;" target="_blank">
                <svg class="nav-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6" />
                    <polyline points="15,3 21,3 21,9" />
                    <line x1="10" y1="14" x2="21" y2="3" />
                </svg>
                前台首頁
            </a>
        </div>
    </aside>

    <main class="main-content">
        <div class="page-header">
            <h1 class="page-title">系統儀表板</h1>
            <div class="user-control">
                <span id="adminUserName">管理員</span>
                <button id="logoutBtn" class="btn btn-outline btn-sm">登出</button>
                <button id="refreshBtn" class="btn btn-primary btn-sm">刷新</button>
            </div>
        </div>

        <!-- 載入狀態 -->
        <div id="dashboardLoading" class="loading-message" style="display: none; text-align: center; padding: 2rem;">
            載入中...
        </div>

        <!-- 統計卡片 -->
        <div class="stats-container">
            <div class="stat-card">
                <div class="stat-icon">📦</div>
                <div class="stat-content">
                    <div class="stat-number" id="totalProjects">0</div>
                    <div class="stat-label">總專案數</div>
                </div>
            </div>

            <div class="stat-card">
                <div class="stat-icon">🟢</div>
                <div class="stat-content">
                    <div class="stat-number" id="activeProjects">0</div>
                    <div class="stat-label">進行中專案</div>
                </div>
            </div>

            <div class="stat-card">
                <div class="stat-icon">📋</div>
                <div class="stat-content">
                    <div class="stat-number" id="totalOrders">0</div>
                    <div class="stat-label">總訂單數</div>
                </div>
            </div>

            <div class="stat-card">
                <div class="stat-icon">💰</div>
                <div class="stat-content">
                    <div class="stat-number" id="totalRevenue">$0</div>
                    <div class="stat-label">總營收</div>
                </div>
            </div>
        </div>

        <!-- 最近專案 -->
        <div class="recent-activity">
            <div class="chart-title">最近專案</div>
            <div class="activity-list" id="recentProjectsList">
                <!-- 專案列表將由 JavaScript 動態載入 -->
            </div>
        </div>

        <!-- 最近訂單 -->
        <div class="recent-activity">
            <div class="chart-title">最近訂單</div>
            <div class="activity-list" id="recentOrdersList">
                <!-- 訂單列表將由 JavaScript 動態載入 -->
            </div>
        </div>

        <!-- 快速操作 -->
        <div class="quick-actions">
            <div class="action-card" id="createProjectBtn">
                <div class="action-icon">📦</div>
                <div class="action-title">新增專案</div>
                <div class="action-desc">建立新的預購專案</div>
            </div>

            <div class="action-card" id="manageOrdersBtn">
                <div class="action-icon">📋</div>
                <div class="action-title">管理訂單</div>
                <div class="action-desc">查看和處理訂單</div>
            </div>

            <div class="action-card" id="manageUsersBtn">
                <div class="action-icon">👥</div>
                <div class="action-title">用戶管理</div>
                <div class="action-desc">管理系統用戶</div>
            </div>
        </div>

        <!-- 最後更新時間 -->
        <div style="text-align: center; margin-top: 2rem; color: #666; font-size: 0.9rem;">
            最後更新：<span id="lastUpdated">--</span>
        </div>
    </main>

    <!-- LIFF SDK -->
    <script charset="utf-8" src="https://static.line-scdn.net/liff/edge/2/sdk.js"></script>

    <!-- 主要初始化腳本 -->
    <script type="module" src="../js/main.js"></script>

    <!-- 管理員儀表板特定腳本 -->
    <script type="module" src="../js/pages/admin/dashboard.js"></script>
</body>
</html>
