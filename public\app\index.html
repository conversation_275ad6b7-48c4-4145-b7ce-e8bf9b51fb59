<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>小森活 - 測試導航頁面</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            padding: 2rem;
        }
        .nav-section {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .nav-section h2 {
            color: #2c3e50;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid #e9ecef;
        }
        .nav-link {
            color: #495057;
            text-decoration: none;
            padding: 0.5rem 1rem;
            margin: 0.25rem 0;
            display: block;
            border-radius: 5px;
            transition: all 0.3s ease;
        }
        .nav-link:hover {
            background-color: #e9ecef;
            color: #0d6efd;
        }
        .header {
            text-align: center;
            margin-bottom: 3rem;
        }
        .header img {
            max-width: 200px;
            margin-bottom: 1rem;
        }
        .test-note {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 1rem;
            margin-top: 2rem;
            color: #856404;
            font-size: 0.9rem;
        }
        .admin-note {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 8px;
            padding: 1rem;
            margin-top: 1rem;
            color: #721c24;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <img src="user/forest-life-logo.png" alt="小森活 Logo">
            <h1>小森活 - 測試導航頁面</h1>
            <div class="test-note">
                <strong>注意：</strong>這是開發測試用的導航頁面<br>
                正式環境中，用戶應該直接進入 <code>/app/user/index.html</code>
            </div>
        </div>

        <div class="nav-section">
            <h2>🏠 用戶端頁面</h2>
            <a href="user/index.html" class="nav-link">🏠 首頁 (真正的應用入口)</a>
            <a href="user/preorder.html" class="nav-link">🛒 預購頁面</a>
            <a href="user/order-history.html" class="nav-link">📋 訂單歷史</a>
            <a href="user/profile.html" class="nav-link">👤 用戶資料</a>
        </div>

        <div class="nav-section">
            <h2>🔐 管理員端頁面</h2>
            <a href="admin/login.html" class="nav-link">🔐 管理員登入 (/admin 路徑)</a>
            <a href="admin/dashboard.html" class="nav-link">📊 管理儀表板</a>
            <a href="admin/management/projects.html" class="nav-link">📦 專案管理</a>
            <a href="admin/management/order-management.html" class="nav-link">📋 訂單管理</a>
            <a href="admin/management/user-management.html" class="nav-link">👥 用戶管理</a>
            <div class="admin-note">
                <strong>管理員路徑說明：</strong><br>
                正式環境中，管理員應該通過 <code>/admin</code> 路徑進入後台系統<br>
                例如：<code>https://yourdomain.com/admin</code> → 重定向到登入頁面
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- LIFF SDK -->
    <script charset="utf-8" src="https://static.line-scdn.net/liff/edge/2/sdk.js"></script>

    <!-- 主要初始化腳本 -->
    <script type="module" src="js/main.js"></script>
</body>
</html>