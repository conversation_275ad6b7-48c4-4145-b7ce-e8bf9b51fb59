/* --- Home Page Specific Styles --- */

/* Adjust main content padding for fixed search bar */
:root {
    --search-bar-height: 58px;
    --card-image-gallery-height: 200px; /* 增加圖片高度 */
    --card-spacing: var(--space-xl); /* 卡片間距 */
}

/* Apply top padding adjustment only to the home page */
.page-home .main-content {
    padding-top: calc(var(--app-header-height) + var(--search-bar-height));
    padding-bottom: calc(var(--bottom-nav-height) + var(--space-xl));
}

.page-home .container {
    display: flex;
    flex-direction: column;
    gap: var(--card-spacing);
}

/* Search Bar */
.page-home .search-container {
    position: fixed;
    top: var(--app-header-height);
    left: 0;
    width: 100%;
    padding: var(--space-sm) var(--space-md);
    background: var(--bg-white);
    display: flex;
    align-items: center;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.04);
    z-index: 999;
    height: var(--search-bar-height);
    border-bottom: 1px solid var(--border-color);
}

.page-home .search-input {
    flex: 1;
    background-color: var(--bg-medium);
    border: 1px solid var(--bg-medium);
    padding-left: var(--space-lg);
    height: calc(100% - 2 * var(--space-sm));
    line-height: normal;
    border-radius: var(--radius-md);
    transition: all 0.2s ease;
}

.page-home .search-input:focus {
    background-color: var(--bg-white);
    box-shadow: 0 0 0 2px var(--primary-lighter);
    border-color: var(--primary-color);
}

.page-home .filter-btn {
    margin-left: var(--space-sm);
    background: none;
    border: none;
    color: var(--text-medium);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    padding: 0;
    cursor: pointer;
    border-radius: var(--radius-sm);
    transition: background-color 0.2s ease;
}

.page-home .filter-btn:hover {
    background-color: var(--bg-light);
}

.page-home .filter-btn .nav-icon {
    width: 24px;
    height: 24px;
}

/* Project Card Specifics for Home */
.page-home .project-card {
    position: relative;
    display: flex;
    flex-direction: column;
    padding: var(--space-xl);
    border-radius: var(--radius-lg);
    background-color: var(--bg-white);
    box-shadow: var(--shadow-sm);
    transition: all 0.3s ease;
    margin: 0; /* 移除邊距，使用 container 的 gap 控制間距 */
}

.page-home .project-card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

/* 卡片內容區塊 */
.page-home .project-card .card-content {
    display: flex;
    flex-direction: column;
    gap: var(--space-lg);
}

/* 標題區塊 */
.page-home .project-card .card-header {
    display: flex;
    flex-direction: column;
    gap: var(--space-md);
}

.page-home .project-card .card-title {
    font-size: var(--font-size-xxl);
    font-weight: 600;
    color: var(--text-dark);
    line-height: 1.3;
}

/* 專案特色 */
.page-home .project-highlights {
    display: flex;
    flex-wrap: wrap;
    gap: var(--space-lg);
}

.page-home .highlight-item {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
}

.page-home .highlight-icon {
    font-size: 24px;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--bg-light);
    border-radius: var(--radius-full);
}

.page-home .highlight-text {
    font-size: var(--font-size-base);
    color: var(--text-dark);
    font-weight: 500;
}

/* 說明文字 */
.page-home .project-card .card-description {
    color: var(--text-medium);
    font-size: var(--font-size-lg);
    line-height: var(--line-height-relaxed);
    margin: var(--space-md) 0;
}

/* 圖片區塊 */
.page-home .project-card .card-image-gallery-container {
    width: 100%;
    position: relative;
    border-radius: var(--radius-lg);
    overflow: hidden;
    background-color: var(--bg-light);
    height: var(--card-image-gallery-height);
}

.page-home .project-card .card-image-gallery {
    display: flex;
    overflow-x: auto;
    scroll-snap-type: x mandatory;
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
    gap: 0;
    height: 100%;
    padding: 0;
    scrollbar-width: none;
    -ms-overflow-style: none;
    align-items: center; /* 垂直置中 */
}

.page-home .project-card .card-image-gallery::-webkit-scrollbar {
    display: none;
}

.page-home .project-card .gallery-image {
    height: 100%;
    width: auto; /* 改為自動寬度 */
    max-width: none; /* 移除最大寬度限制 */
    object-fit: contain; /* 改為 contain 以保持原比例 */
    scroll-snap-align: start;
    flex-shrink: 0;
}

/* 圖片導航點位置調整 */
.page-home .gallery-dots {
    position: absolute;
    bottom: var(--space-sm);
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: var(--space-xs);
    z-index: 1;
    background-color: rgba(0, 0, 0, 0.3); /* 添加半透明背景 */
    padding: 4px 8px;
    border-radius: var(--radius-full);
}

.page-home .gallery-dot {
    width: 8px;
    height: 8px;
    border-radius: var(--radius-full);
    background-color: rgba(255, 255, 255, 0.5);
    transition: all 0.3s ease;
}

.page-home .gallery-dot.active {
    background-color: var(--bg-white);
    width: 24px;
}

/* 底部資訊 */
.page-home .project-card .card-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: var(--space-lg);
    padding-top: var(--space-lg);
    border-top: 1px solid var(--border-light);
}

/* 價格資訊 */
.page-home .project-card .project-price {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--secondary-color);
}

/* 查看詳情按鈕 */
.page-home .project-card .btn-primary {
    padding: var(--space-sm) var(--space-xl);
    font-size: var(--font-size-base);
    font-weight: 500;
}

/* 狀態標籤 */
.page-home .project-status {
    position: absolute;
    top: var(--space-lg);
    right: var(--space-lg);
    padding: var(--space-sm) var(--space-lg);
    border-radius: var(--radius-full);
    font-size: var(--font-size-base);
    font-weight: 500;
    z-index: 1;
}

.page-home .status-active {
    background-color: var(--primary-light);
    color: var(--primary-color);
}

.page-home .status-ordering_ended {
    background-color: var(--warning-light);
    color: var(--warning-color);
}

.page-home .status-arrived {
    background-color: var(--success-light);
    color: var(--success-color);
}

.page-home .status-completed {
    background-color: var(--text-light);
    color: var(--text-medium);
}

/* 響應式調整 */
@media (max-width: 768px) {
    .page-home .project-card {
        padding: var(--space-lg);
    }

    .page-home .project-card .card-title {
        font-size: var(--font-size-xl);
    }

    .page-home .project-card .card-description {
        font-size: var(--font-size-base);
    }

    .page-home .project-highlights {
        gap: var(--space-md);
    }

    .page-home .highlight-icon {
        font-size: 20px;
        width: 32px;
        height: 32px;
    }

    .page-home .highlight-text {
        font-size: var(--font-size-sm);
    }

    .page-home .project-card .project-price {
        font-size: var(--font-size-lg);
    }
}

/* 載入更多區域 */
.page-home .load-more {
    text-align: center;
    padding: var(--space-xl) 0;
    color: var(--text-medium);
    font-size: var(--font-size-base);
}