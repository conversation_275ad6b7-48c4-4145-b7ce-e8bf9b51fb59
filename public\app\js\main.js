/**
 * 主要初始化腳本
 * 負責全局初始化、LIFF 初始化、共用事件監聽器等
 */

import { initializeLiff } from './services/liffService.js';
import { checkSupabaseConnection, initializeSupabase } from './services/supabaseService.js';
import { userInfo, isLoggedIn, liffInitialized } from './store/authStore.js';
import { totalCount } from './store/cartStore.js';
import { ready, $, setContent } from './utils/domUtils.js';

// 全局狀態
let isInitialized = false;

/**
 * 主要初始化函數
 */
async function initialize() {
  if (isInitialized) {
    console.log('App already initialized');
    return;
  }

  console.log('Initializing Small Forest Life app...');

  try {
    // 1. 初始化 Supabase
    console.log('Initializing Supabase...');
    initializeSupabase();

    // 2. 檢查 Supabase 連接
    console.log('Checking Supabase connection...');
    const supabaseStatus = await checkSupabaseConnection();
    if (!supabaseStatus.ok) {
      console.error('Supabase connection failed:', supabaseStatus.error);
      showConnectionError('資料庫連接失敗，請稍後再試');
      return;
    }
    console.log('Supabase connection successful');

    // 3. 初始化 LIFF
    console.log('Initializing LIFF...');
    const liffStatus = await initializeLiff();
    if (!liffStatus) {
      console.warn('LIFF initialization failed, continuing without LIFF features');
    } else {
      console.log('LIFF initialization successful');
    }

    // 4. 設置狀態監聽器
    setupStoreListeners();

    // 5. 設置全局事件監聽器
    setupGlobalEventListeners();

    // 6. 初始化頁面特定功能
    initializePageSpecificFeatures();

    isInitialized = true;
    console.log('App initialization completed');

  } catch (error) {
    console.error('App initialization failed:', error);
    showConnectionError('應用程式初始化失敗，請重新整理頁面');
  }
}

/**
 * 設置狀態監聽器
 */
function setupStoreListeners() {
  // 監聽用戶登入狀態變化
  userInfo.subscribe((user) => {
    updateUserUI(user);
  });

  // 監聽購物車數量變化
  totalCount.subscribe((count) => {
    updateCartBadge(count);
  });

  // 監聽 LIFF 初始化狀態
  liffInitialized.subscribe((initialized) => {
    updateLiffStatus(initialized);
  });
}

/**
 * 更新用戶 UI
 * @param {Object|null} user - 用戶資訊
 */
function updateUserUI(user) {
  const userControls = document.querySelectorAll('.user-control');
  const userAvatars = document.querySelectorAll('.user-avatar');

  userControls.forEach(control => {
    if (user) {
      control.style.display = 'block';
    } else {
      control.style.display = 'none';
    }
  });

  userAvatars.forEach(avatar => {
    if (user) {
      const img = avatar.querySelector('img');
      const span = avatar.querySelector('span');

      if (user.pictureUrl) {
        if (img) {
          img.src = user.pictureUrl;
          img.style.display = 'block';
        } else {
          const newImg = document.createElement('img');
          newImg.src = user.pictureUrl;
          newImg.alt = '用戶頭像';
          avatar.appendChild(newImg);
        }
        if (span) span.style.display = 'none';
      } else {
        if (img) img.style.display = 'none';
        if (span) {
          span.textContent = user.displayName ? user.displayName.charAt(0).toUpperCase() : 'U';
          span.style.display = 'block';
        }
      }
    }
  });
}

/**
 * 更新購物車徽章
 * @param {number} count - 購物車數量
 */
function updateCartBadge(count) {
  const badges = document.querySelectorAll('.cart-badge');
  badges.forEach(badge => {
    setContent(badge, count.toString());
    if (count > 0) {
      badge.style.display = 'block';
    } else {
      badge.style.display = 'none';
    }
  });
}

/**
 * 更新 LIFF 狀態
 * @param {boolean} initialized - 是否已初始化
 */
function updateLiffStatus(initialized) {
  // 可以在這裡添加 LIFF 狀態相關的 UI 更新
  console.log('LIFF status updated:', initialized);
}

/**
 * 設置全局事件監聽器
 */
function setupGlobalEventListeners() {
  // 處理導航點擊
  document.addEventListener('click', (event) => {
    const target = event.target.closest('a[href]');
    if (target) {
      handleNavigation(event, target);
    }
  });

  // 處理表單提交
  document.addEventListener('submit', (event) => {
    handleFormSubmit(event);
  });

  // 處理視窗大小變化
  window.addEventListener('resize', debounce(() => {
    handleWindowResize();
  }, 250));

  // 處理頁面可見性變化
  document.addEventListener('visibilitychange', () => {
    handleVisibilityChange();
  });
}

/**
 * 處理導航
 * @param {Event} event - 點擊事件
 * @param {Element} target - 目標元素
 */
function handleNavigation(event, target) {
  const href = target.getAttribute('href');

  // 如果是外部連結或特殊協議，不處理
  if (!href || href.startsWith('http') || href.startsWith('mailto') || href.startsWith('tel')) {
    return;
  }

  // 可以在這裡添加頁面切換的特殊處理
  console.log('Navigating to:', href);
}

/**
 * 處理表單提交
 * @param {Event} event - 提交事件
 */
function handleFormSubmit(event) {
  const form = event.target;

  // 可以在這裡添加全局表單驗證或處理
  console.log('Form submitted:', form);
}

/**
 * 處理視窗大小變化
 */
function handleWindowResize() {
  // 可以在這裡添加響應式處理
  console.log('Window resized:', window.innerWidth, window.innerHeight);
}

/**
 * 處理頁面可見性變化
 */
function handleVisibilityChange() {
  if (document.hidden) {
    console.log('Page hidden');
  } else {
    console.log('Page visible');
    // 頁面重新可見時，可以刷新數據
  }
}

/**
 * 初始化頁面特定功能
 */
function initializePageSpecificFeatures() {
  const currentPage = getCurrentPage();
  console.log('Current page:', currentPage);

  // 根據當前頁面初始化特定功能
  switch (currentPage) {
    case 'home':
      initializeHomePage();
      break;
    case 'preorder':
      initializePreorderPage();
      break;
    case 'order-history':
      initializeOrderHistoryPage();
      break;
    case 'admin-login':
      initializeAdminLoginPage();
      break;
    case 'admin-dashboard':
      initializeAdminDashboardPage();
      break;
    default:
      console.log('No specific initialization for this page');
  }
}

/**
 * 獲取當前頁面類型
 * @returns {string} 頁面類型
 */
function getCurrentPage() {
  const path = window.location.pathname;
  const filename = path.split('/').pop();

  if (filename.includes('index.html') || path.endsWith('/user/')) {
    return 'home';
  } else if (filename.includes('preorder.html')) {
    return 'preorder';
  } else if (filename.includes('order-history.html')) {
    return 'order-history';
  } else if (filename.includes('admin/login.html')) {
    return 'admin-login';
  } else if (filename.includes('admin/dashboard.html')) {
    return 'admin-dashboard';
  }

  return 'unknown';
}

/**
 * 初始化首頁
 */
function initializeHomePage() {
  console.log('Initializing home page...');
  // 首頁特定的初始化邏輯將在 pages/user/home.js 中實現
}

/**
 * 初始化預購頁面
 */
function initializePreorderPage() {
  console.log('Initializing preorder page...');
  // 預購頁面特定的初始化邏輯將在 pages/user/preorder.js 中實現
}

/**
 * 初始化訂單歷史頁面
 */
function initializeOrderHistoryPage() {
  console.log('Initializing order history page...');
  // 訂單歷史頁面特定的初始化邏輯將在 pages/user/orderHistory.js 中實現
}

/**
 * 初始化管理員登入頁面
 */
function initializeAdminLoginPage() {
  console.log('Initializing admin login page...');
  // 管理員登入頁面特定的初始化邏輯將在 pages/admin/login.js 中實現
}

/**
 * 初始化管理員儀表板頁面
 */
function initializeAdminDashboardPage() {
  console.log('Initializing admin dashboard page...');
  // 管理員儀表板頁面特定的初始化邏輯將在 pages/admin/dashboard.js 中實現
}

/**
 * 顯示連接錯誤
 * @param {string} message - 錯誤訊息
 */
function showConnectionError(message) {
  // 創建錯誤提示元素
  const errorDiv = document.createElement('div');
  errorDiv.className = 'connection-error';
  errorDiv.style.cssText = `
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: #f44336;
    color: white;
    padding: 12px 24px;
    border-radius: 4px;
    z-index: 10000;
    font-size: 14px;
  `;
  errorDiv.textContent = message;

  document.body.appendChild(errorDiv);

  // 5秒後自動移除
  setTimeout(() => {
    if (errorDiv.parentNode) {
      errorDiv.parentNode.removeChild(errorDiv);
    }
  }, 5000);
}

/**
 * 防抖函數
 * @param {Function} func - 要防抖的函數
 * @param {number} wait - 等待時間
 * @returns {Function} 防抖後的函數
 */
function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

// 當 DOM 載入完成時初始化應用程式
ready(initialize);

// 導出初始化函數供其他模組使用
export { initialize };
