/**
 * 首頁 JavaScript 邏輯
 * 負責專案列表顯示、搜尋、篩選等功能
 */

import { fetchActiveProjects } from '../../services/supabaseService.js';
import { formatDate, formatDateTime, isPast, isFuture } from '../../utils/dateUtils.js';
import { formatAmount } from '../../utils/discountCalculator.js';
import { $, $$, createElement, setContent, addClass, removeClass, on, ready } from '../../utils/domUtils.js';

// 頁面狀態
let projects = [];
let filteredProjects = [];
let isLoading = false;

/**
 * 初始化首頁
 */
async function initializeHomePage() {
  console.log('Initializing home page...');
  
  try {
    // 設置事件監聽器
    setupEventListeners();
    
    // 載入專案列表
    await loadProjects();
    
    // 初始化搜尋功能
    initializeSearch();
    
    console.log('Home page initialized successfully');
  } catch (error) {
    console.error('Error initializing home page:', error);
    showError('頁面初始化失敗，請重新整理頁面');
  }
}

/**
 * 設置事件監聽器
 */
function setupEventListeners() {
  // 搜尋輸入框
  const searchInput = $('.search-input');
  if (searchInput) {
    on(searchInput, 'input', debounceSearch);
    on(searchInput, 'keypress', (event) => {
      if (event.key === 'Enter') {
        event.preventDefault();
        performSearch();
      }
    });
  }

  // 搜尋按鈕
  const searchBtn = $('.filter-btn');
  if (searchBtn) {
    on(searchBtn, 'click', performSearch);
  }

  // 專案卡片點擊事件（事件委託）
  const container = $('.container');
  if (container) {
    on(container, 'click', handleProjectCardClick);
  }
}

/**
 * 載入專案列表
 */
async function loadProjects() {
  if (isLoading) return;
  
  isLoading = true;
  showLoading(true);
  
  try {
    console.log('Loading projects...');
    projects = await fetchActiveProjects();
    filteredProjects = [...projects];
    
    console.log('Projects loaded:', projects.length);
    renderProjects(filteredProjects);
    
  } catch (error) {
    console.error('Error loading projects:', error);
    showError('載入專案失敗，請稍後再試');
  } finally {
    isLoading = false;
    showLoading(false);
  }
}

/**
 * 渲染專案列表
 * @param {Array} projectsToRender - 要渲染的專案列表
 */
function renderProjects(projectsToRender) {
  const container = $('.container');
  if (!container) return;

  // 清除現有的專案卡片（保留搜尋欄）
  const existingCards = $$('.project-card');
  existingCards.forEach(card => card.remove());

  // 移除載入更多按鈕
  const loadMore = $('.load-more');
  if (loadMore) loadMore.remove();

  if (projectsToRender.length === 0) {
    // 顯示無專案訊息
    const noProjectsDiv = createElement('div', {
      className: 'no-projects-message',
      style: 'text-align: center; padding: 2rem; color: #666;'
    }, '目前沒有進行中的專案');
    
    container.appendChild(noProjectsDiv);
    return;
  }

  // 渲染專案卡片
  projectsToRender.forEach(project => {
    const projectCard = createProjectCard(project);
    container.appendChild(projectCard);
  });

  // 添加載入更多訊息
  const loadMoreDiv = createElement('div', {
    className: 'load-more'
  }, [
    createElement('span', {}, '沒有更多專案了')
  ]);
  
  container.appendChild(loadMoreDiv);
}

/**
 * 創建專案卡片
 * @param {Object} project - 專案資料
 * @returns {Element} 專案卡片元素
 */
function createProjectCard(project) {
  // 獲取專案狀態顯示
  const statusInfo = getProjectStatusInfo(project);
  
  // 創建亮點元素
  const highlightsElements = (project.highlights || []).slice(0, 3).map(highlight => 
    createElement('div', { className: 'highlight-item' }, [
      createElement('div', { className: 'highlight-icon' }, highlight.icon),
      createElement('span', { className: 'highlight-text' }, highlight.text)
    ])
  );

  // 創建圖片畫廊
  const galleryImages = (project.images || []).map(imageUrl =>
    createElement('img', {
      src: imageUrl,
      alt: project.name,
      className: 'gallery-image',
      loading: 'lazy'
    })
  );

  // 創建圖片點點
  const galleryDots = (project.images || []).map((_, index) =>
    createElement('span', {
      className: index === 0 ? 'gallery-dot active' : 'gallery-dot'
    })
  );

  // 創建專案卡片
  const projectCard = createElement('div', {
    className: 'project-card',
    dataset: { projectId: project.id }
  }, [
    createElement('div', {
      className: `project-status ${statusInfo.className}`
    }, statusInfo.label),
    
    createElement('div', { className: 'card-content' }, [
      createElement('div', { className: 'card-header' }, [
        createElement('h2', { className: 'card-title' }, project.name),
        createElement('div', { className: 'project-highlights' }, highlightsElements)
      ]),
      
      createElement('p', { className: 'card-description' }, project.description || ''),
      
      createElement('div', { className: 'card-image-gallery-container' }, [
        createElement('div', { className: 'card-image-gallery' }, galleryImages),
        createElement('div', { className: 'gallery-dots' }, galleryDots)
      ]),
      
      createElement('div', { className: 'card-footer' }, [
        createElement('div', { className: 'project-price' }, 
          project.minPrice ? `商品 $${formatAmount(project.minPrice)} 起` : '價格待定'
        ),
        createElement('a', {
          href: `preorder.html?id=${project.id}`,
          className: 'btn btn-primary'
        }, '查看詳情')
      ])
    ])
  ]);

  // 如果有多張圖片，設置圖片輪播
  if (galleryImages.length > 1) {
    setupImageGallery(projectCard);
  }

  return projectCard;
}

/**
 * 獲取專案狀態資訊
 * @param {Object} project - 專案資料
 * @returns {Object} 狀態資訊
 */
function getProjectStatusInfo(project) {
  switch (project.project_status) {
    case 'active':
      return {
        label: '進行中',
        className: 'status-active'
      };
    case 'ordering_ended':
      return {
        label: '結束預購',
        className: 'status-ordering_ended'
      };
    case 'arrived':
      return {
        label: '已到貨',
        className: 'status-arrived'
      };
    case 'completed':
      return {
        label: '已結束',
        className: 'status-completed'
      };
    default:
      return {
        label: '未知狀態',
        className: 'status-unknown'
      };
  }
}

/**
 * 設置圖片畫廊輪播
 * @param {Element} projectCard - 專案卡片元素
 */
function setupImageGallery(projectCard) {
  const gallery = projectCard.querySelector('.card-image-gallery');
  const dots = projectCard.querySelectorAll('.gallery-dot');
  
  if (!gallery || dots.length === 0) return;

  let currentIndex = 0;
  
  // 點點點擊事件
  dots.forEach((dot, index) => {
    on(dot, 'click', () => {
      currentIndex = index;
      updateGallery();
    });
  });

  // 自動輪播
  let autoPlayInterval = setInterval(() => {
    currentIndex = (currentIndex + 1) % dots.length;
    updateGallery();
  }, 5000);

  // 滑鼠懸停時停止自動輪播
  on(gallery, 'mouseenter', () => {
    clearInterval(autoPlayInterval);
  });

  on(gallery, 'mouseleave', () => {
    autoPlayInterval = setInterval(() => {
      currentIndex = (currentIndex + 1) % dots.length;
      updateGallery();
    }, 5000);
  });

  function updateGallery() {
    const images = gallery.querySelectorAll('.gallery-image');
    
    images.forEach((img, index) => {
      img.style.display = index === currentIndex ? 'block' : 'none';
    });
    
    dots.forEach((dot, index) => {
      if (index === currentIndex) {
        addClass(dot, 'active');
      } else {
        removeClass(dot, 'active');
      }
    });
  }
}

/**
 * 處理專案卡片點擊
 * @param {Event} event - 點擊事件
 */
function handleProjectCardClick(event) {
  const projectCard = event.target.closest('.project-card');
  if (!projectCard) return;

  // 如果點擊的是連結，不處理
  if (event.target.closest('a')) return;

  const projectId = projectCard.dataset.projectId;
  if (projectId) {
    window.location.href = `preorder.html?id=${projectId}`;
  }
}

/**
 * 初始化搜尋功能
 */
function initializeSearch() {
  console.log('Search functionality initialized');
}

/**
 * 防抖搜尋
 */
const debounceSearch = debounce(performSearch, 300);

/**
 * 執行搜尋
 */
function performSearch() {
  const searchInput = $('.search-input');
  if (!searchInput) return;

  const searchTerm = searchInput.value.trim().toLowerCase();
  
  if (!searchTerm) {
    // 如果搜尋詞為空，顯示所有專案
    filteredProjects = [...projects];
  } else {
    // 根據專案名稱和描述進行搜尋
    filteredProjects = projects.filter(project => {
      const nameMatch = project.name.toLowerCase().includes(searchTerm);
      const descriptionMatch = project.description && 
                              project.description.toLowerCase().includes(searchTerm);
      return nameMatch || descriptionMatch;
    });
  }
  
  console.log(`Search results: ${filteredProjects.length} projects found`);
  renderProjects(filteredProjects);
}

/**
 * 顯示載入狀態
 * @param {boolean} show - 是否顯示載入狀態
 */
function showLoading(show) {
  const container = $('.container');
  if (!container) return;

  let loadingDiv = $('.loading-message');
  
  if (show) {
    if (!loadingDiv) {
      loadingDiv = createElement('div', {
        className: 'loading-message',
        style: 'text-align: center; padding: 2rem; color: #666;'
      }, '載入中...');
      container.appendChild(loadingDiv);
    }
  } else {
    if (loadingDiv) {
      loadingDiv.remove();
    }
  }
}

/**
 * 顯示錯誤訊息
 * @param {string} message - 錯誤訊息
 */
function showError(message) {
  const container = $('.container');
  if (!container) return;

  const errorDiv = createElement('div', {
    className: 'error-message',
    style: 'text-align: center; padding: 2rem; color: #f44336; background: #ffebee; border-radius: 4px; margin: 1rem 0;'
  }, message);
  
  container.appendChild(errorDiv);
  
  // 5秒後自動移除
  setTimeout(() => {
    if (errorDiv.parentNode) {
      errorDiv.remove();
    }
  }, 5000);
}

/**
 * 防抖函數
 * @param {Function} func - 要防抖的函數
 * @param {number} wait - 等待時間
 * @returns {Function} 防抖後的函數
 */
function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

// 當 DOM 載入完成時初始化首頁
ready(() => {
  // 檢查是否為首頁
  const isHomePage = window.location.pathname.includes('user/index.html') || 
                     window.location.pathname.endsWith('/user/');
  
  if (isHomePage) {
    initializeHomePage();
  }
});

// 導出函數供其他模組使用
export { initializeHomePage, loadProjects, performSearch };
