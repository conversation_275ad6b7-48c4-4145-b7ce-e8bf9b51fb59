/**
 * Cloudinary 服務層
 * 處理圖片上傳到 Cloudinary
 */

// Cloudinary 配置從環境變數獲取
const CLOUDINARY_CLOUD_NAME = import.meta.env.VITE_CLOUDINARY_CLOUD_NAME;
const CLOUDINARY_UPLOAD_PRESET = import.meta.env.VITE_CLOUDINARY_UPLOAD_PRESET;

if (!CLOUDINARY_CLOUD_NAME || !CLOUDINARY_UPLOAD_PRESET) {
  console.error('Missing Cloudinary environment variables');
}

/**
 * 上傳圖片到 Cloudinary
 * @param {File} file - 要上傳的圖片檔案
 * @param {Object} options - 上傳選項
 * @returns {Promise<string>} 上傳後的圖片 URL
 */
export async function uploadImage(file, options = {}) {
  try {
    // 驗證檔案
    if (!file) {
      throw new Error('No file provided');
    }

    // 檢查檔案類型
    if (!file.type.startsWith('image/')) {
      throw new Error('File must be an image');
    }

    // 檢查檔案大小 (預設最大 10MB)
    const maxSize = options.maxSize || 10 * 1024 * 1024; // 10MB
    if (file.size > maxSize) {
      throw new Error(`File size must be less than ${maxSize / 1024 / 1024}MB`);
    }

    // 準備上傳資料
    const formData = new FormData();
    formData.append('file', file);
    formData.append('upload_preset', CLOUDINARY_UPLOAD_PRESET);
    
    // 可選參數
    if (options.folder) {
      formData.append('folder', options.folder);
    }
    
    if (options.publicId) {
      formData.append('public_id', options.publicId);
    }

    if (options.tags) {
      formData.append('tags', options.tags.join(','));
    }

    // 上傳到 Cloudinary
    const uploadUrl = `https://api.cloudinary.com/v1_1/${CLOUDINARY_CLOUD_NAME}/image/upload`;
    
    const response = await fetch(uploadUrl, {
      method: 'POST',
      body: formData
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error?.message || 'Upload failed');
    }

    const result = await response.json();
    
    // 返回安全的 HTTPS URL
    return result.secure_url;
  } catch (error) {
    console.error('Error uploading image:', error);
    throw error;
  }
}

/**
 * 批量上傳圖片
 * @param {FileList|Array} files - 要上傳的圖片檔案列表
 * @param {Object} options - 上傳選項
 * @returns {Promise<Array>} 上傳後的圖片 URL 陣列
 */
export async function uploadMultipleImages(files, options = {}) {
  try {
    const fileArray = Array.from(files);
    
    if (fileArray.length === 0) {
      return [];
    }

    // 並行上傳所有圖片
    const uploadPromises = fileArray.map((file, index) => {
      const fileOptions = {
        ...options,
        publicId: options.publicId ? `${options.publicId}_${index}` : undefined
      };
      return uploadImage(file, fileOptions);
    });

    const results = await Promise.all(uploadPromises);
    return results;
  } catch (error) {
    console.error('Error uploading multiple images:', error);
    throw error;
  }
}

/**
 * 生成 Cloudinary 轉換 URL
 * @param {string} imageUrl - 原始圖片 URL
 * @param {Object} transformations - 轉換參數
 * @returns {string} 轉換後的 URL
 */
export function getTransformedImageUrl(imageUrl, transformations = {}) {
  try {
    if (!imageUrl || !imageUrl.includes('cloudinary.com')) {
      return imageUrl;
    }

    // 解析 URL
    const urlParts = imageUrl.split('/');
    const uploadIndex = urlParts.findIndex(part => part === 'upload');
    
    if (uploadIndex === -1) {
      return imageUrl;
    }

    // 構建轉換參數
    const transformParams = [];
    
    if (transformations.width) {
      transformParams.push(`w_${transformations.width}`);
    }
    
    if (transformations.height) {
      transformParams.push(`h_${transformations.height}`);
    }
    
    if (transformations.crop) {
      transformParams.push(`c_${transformations.crop}`);
    }
    
    if (transformations.quality) {
      transformParams.push(`q_${transformations.quality}`);
    }
    
    if (transformations.format) {
      transformParams.push(`f_${transformations.format}`);
    }

    // 如果沒有轉換參數，返回原始 URL
    if (transformParams.length === 0) {
      return imageUrl;
    }

    // 插入轉換參數
    const transformString = transformParams.join(',');
    urlParts.splice(uploadIndex + 1, 0, transformString);
    
    return urlParts.join('/');
  } catch (error) {
    console.error('Error generating transformed URL:', error);
    return imageUrl;
  }
}

/**
 * 獲取縮圖 URL
 * @param {string} imageUrl - 原始圖片 URL
 * @param {number} size - 縮圖尺寸
 * @returns {string} 縮圖 URL
 */
export function getThumbnailUrl(imageUrl, size = 200) {
  return getTransformedImageUrl(imageUrl, {
    width: size,
    height: size,
    crop: 'fill',
    quality: 'auto',
    format: 'auto'
  });
}

/**
 * 獲取響應式圖片 URL
 * @param {string} imageUrl - 原始圖片 URL
 * @param {number} maxWidth - 最大寬度
 * @returns {string} 響應式圖片 URL
 */
export function getResponsiveImageUrl(imageUrl, maxWidth = 800) {
  return getTransformedImageUrl(imageUrl, {
    width: maxWidth,
    crop: 'scale',
    quality: 'auto',
    format: 'auto'
  });
}

/**
 * 驗證圖片檔案
 * @param {File} file - 檔案對象
 * @param {Object} options - 驗證選項
 * @returns {Object} 驗證結果
 */
export function validateImageFile(file, options = {}) {
  const result = {
    valid: true,
    errors: []
  };

  if (!file) {
    result.valid = false;
    result.errors.push('No file provided');
    return result;
  }

  // 檢查檔案類型
  const allowedTypes = options.allowedTypes || ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
  if (!allowedTypes.includes(file.type)) {
    result.valid = false;
    result.errors.push(`File type ${file.type} not allowed. Allowed types: ${allowedTypes.join(', ')}`);
  }

  // 檢查檔案大小
  const maxSize = options.maxSize || 10 * 1024 * 1024; // 10MB
  if (file.size > maxSize) {
    result.valid = false;
    result.errors.push(`File size ${(file.size / 1024 / 1024).toFixed(2)}MB exceeds maximum ${(maxSize / 1024 / 1024).toFixed(2)}MB`);
  }

  const minSize = options.minSize || 0;
  if (file.size < minSize) {
    result.valid = false;
    result.errors.push(`File size ${(file.size / 1024).toFixed(2)}KB is below minimum ${(minSize / 1024).toFixed(2)}KB`);
  }

  return result;
}

/**
 * 從 URL 刪除 Cloudinary 圖片
 * @param {string} imageUrl - 圖片 URL
 * @returns {Promise<boolean>} 刪除是否成功
 */
export async function deleteImage(imageUrl) {
  try {
    // 注意：刪除圖片需要 Cloudinary Admin API，通常在後端實現
    // 這裡只是一個佔位符函數
    console.warn('Image deletion should be implemented on the backend using Cloudinary Admin API');
    return false;
  } catch (error) {
    console.error('Error deleting image:', error);
    return false;
  }
}
