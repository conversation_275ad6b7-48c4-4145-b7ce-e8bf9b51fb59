<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>小森活後台 - 管理員登入</title>
    <style>
        /* 基本設置 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Noto Sans TC', 'Roboto', sans-serif;
        }

        body {
            background-color: #FDFDFD;
            color: #333333;
            line-height: 1.6;
            font-size: 16px;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
        }

        .login-container {
            background-color: white;
            border-radius: 12px;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
            width: 90%;
            max-width: 420px;
            padding: 2rem;
            text-align: center;
        }

        .logo {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 1.5rem;
        }

        .logo-img {
            width: 32px;
            height: 32px;
            margin-right: 8px;
            object-fit: contain;
        }

        .logo-text {
            font-size: 18px;
            font-weight: 600;
            color: #57AC5A;
        }

        h1 {
            color: #57AC5A;
            font-size: 1.8rem;
            margin-bottom: 1rem;
            font-weight: 700;
        }

        .subtitle {
            color: #666;
            margin-bottom: 2rem;
        }

        .login-btn {
            background-color: #57AC5A;
            color: white;
            border: none;
            border-radius: 8px;
            padding: 0.8rem 2rem;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            margin-bottom: 1.5rem;
            box-shadow: 0 2px 8px rgba(87, 172, 90, 0.25);
        }

        .login-btn svg {
            margin-right: 0.5rem;
        }

        .login-btn:hover {
            background-color: #aedfc0;
            box-shadow: 0 4px 12px rgba(87, 172, 90, 0.3);
        }

        .note {
            font-size: 0.85rem;
            color: #777;
            margin-top: 1rem;
        }

        .error-message {
            color: #e74c3c;
            background-color: rgba(231, 76, 60, 0.1);
            padding: 0.75rem;
            border-radius: 8px;
            margin-bottom: 1.5rem;
            display: none;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="logo">
            <img src="../user/forest-life-logo.png" alt="小森活" class="logo-img">
            <span class="logo-text">小森活</span>
        </div>
        <h1>管理後台登入</h1>
        <p class="subtitle">使用LINE帳號登入管理專案、訂單與用戶</p>

        <div class="error-message">
            抱歉，您沒有管理員權限。請聯絡系統管理員申請權限。
        </div>

        <button class="login-btn">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M9 12h6m-6-4h6m-6 8h6M7.2 4h9.6a2.4 2.4 0 0 1 2.4 2.4v15.2a2.4 2.4 0 0 1-2.4 2.4H7.2a2.4 2.4 0 0 1-2.4-2.4V6.4A2.4 2.4 0 0 1 7.2 4z"/>
            </svg>
            使用 LINE 帳號登入
        </button>

        <p class="note">※ 僅限管理員與負責人使用</p>
        <p class="note">※ 確保在 LINE App 中開啟此頁面</p>

        <!-- 快速跳轉前台 -->
        <div style="margin-top: 2rem; text-align: center;">
            <a href="../user/index.html" style="color: #57AC5A; text-decoration: none; font-size: 0.9rem;" target="_blank">
                🏠 返回前台首頁
            </a>
        </div>
    </div>

    <!-- LIFF SDK -->
    <script charset="utf-8" src="https://static.line-scdn.net/liff/edge/2/sdk.js"></script>

    <!-- 主要初始化腳本 -->
    <script type="module" src="../js/main.js"></script>

    <!-- 管理員登入頁面特定腳本 -->
    <script type="module" src="../js/pages/admin/login.js"></script>
</body>
</html>