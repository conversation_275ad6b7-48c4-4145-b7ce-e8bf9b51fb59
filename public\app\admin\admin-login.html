<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理員登入 - 小森活預購系統</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .login-container {
            background: white;
            border-radius: 12px;
            padding: 2rem;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 400px;
        }
        
        .logo {
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .logo h1 {
            color: #57AC5A;
            margin: 0;
            font-size: 1.8rem;
            font-weight: 600;
        }
        
        .logo p {
            color: #666;
            margin: 0.5rem 0 0 0;
            font-size: 0.9rem;
        }
        
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            color: #333;
            font-weight: 500;
        }
        
        .form-input {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 1rem;
            transition: border-color 0.2s;
            box-sizing: border-box;
        }
        
        .form-input:focus {
            outline: none;
            border-color: #57AC5A;
        }
        
        .btn {
            width: 100%;
            padding: 0.75rem;
            background-color: #57AC5A;
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        
        .btn:hover {
            background-color: #4a9c4d;
        }
        
        .btn:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        
        .status-message {
            margin-top: 1rem;
            padding: 0.75rem;
            border-radius: 6px;
            text-align: center;
            font-size: 0.9rem;
        }
        
        .status-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status-error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status-info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .quick-links {
            margin-top: 2rem;
            text-align: center;
        }
        
        .quick-links a {
            color: #57AC5A;
            text-decoration: none;
            font-size: 0.9rem;
            margin: 0 0.5rem;
        }
        
        .quick-links a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="logo">
            <h1>🌲 小森活</h1>
            <p>管理員登入</p>
        </div>
        
        <form id="loginForm">
            <div class="form-group">
                <label class="form-label" for="adminId">管理員 ID</label>
                <input type="text" id="adminId" class="form-input" value="admin_test_user" placeholder="請輸入管理員 ID">
            </div>
            
            <button type="submit" class="btn" id="loginBtn">登入</button>
        </form>
        
        <div id="statusMessage" class="status-message" style="display: none;"></div>
        
        <div class="quick-links">
            <a href="test-orders.html">測試頁面</a>
            <a href="management/order-management.html">訂單管理</a>
            <a href="../user/index.html">前台首頁</a>
        </div>
    </div>

    <!-- Supabase CDN -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    
    <script type="module">
        import {
            initializeSupabase,
            authenticateAsAdmin,
            isCurrentUserAdmin,
            getCurrentAdminUser,
            logoutAdmin
        } from '../js/services/supabaseService.js';
        
        // 初始化
        document.addEventListener('DOMContentLoaded', () => {
            initializeSupabase();
            checkCurrentAuth();
            setupEventListeners();
        });
        
        // 檢查當前身份驗證狀態
        function checkCurrentAuth() {
            if (isCurrentUserAdmin()) {
                const user = getCurrentAdminUser();
                showStatus(`已登入：${user.display_name}`, 'success');
                document.getElementById('loginBtn').textContent = '重新登入';
            }
        }
        
        // 設置事件監聽器
        function setupEventListeners() {
            const form = document.getElementById('loginForm');
            const loginBtn = document.getElementById('loginBtn');
            
            form.addEventListener('submit', async (e) => {
                e.preventDefault();
                await handleLogin();
            });
            
            // 添加登出按鈕
            if (isCurrentUserAdmin()) {
                addLogoutButton();
            }
        }
        
        // 處理登入
        async function handleLogin() {
            const adminId = document.getElementById('adminId').value.trim();
            const loginBtn = document.getElementById('loginBtn');
            
            if (!adminId) {
                showStatus('請輸入管理員 ID', 'error');
                return;
            }
            
            loginBtn.disabled = true;
            loginBtn.textContent = '登入中...';
            showStatus('正在驗證身份...', 'info');
            
            try {
                const success = await authenticateAsAdmin(adminId);
                
                if (success) {
                    const user = getCurrentAdminUser();
                    showStatus(`登入成功！歡迎 ${user.display_name}`, 'success');
                    loginBtn.textContent = '重新登入';
                    addLogoutButton();
                    
                    // 3秒後跳轉到訂單管理頁面
                    setTimeout(() => {
                        window.location.href = 'management/order-management.html';
                    }, 3000);
                } else {
                    showStatus('登入失敗：無效的管理員 ID 或權限不足', 'error');
                }
            } catch (error) {
                showStatus('登入失敗：' + error.message, 'error');
            } finally {
                loginBtn.disabled = false;
                if (loginBtn.textContent === '登入中...') {
                    loginBtn.textContent = '登入';
                }
            }
        }
        
        // 顯示狀態訊息
        function showStatus(message, type) {
            const statusDiv = document.getElementById('statusMessage');
            statusDiv.textContent = message;
            statusDiv.className = `status-message status-${type}`;
            statusDiv.style.display = 'block';
        }
        
        // 添加登出按鈕
        function addLogoutButton() {
            if (document.getElementById('logoutBtn')) return;
            
            const logoutBtn = document.createElement('button');
            logoutBtn.id = 'logoutBtn';
            logoutBtn.type = 'button';
            logoutBtn.className = 'btn';
            logoutBtn.textContent = '登出';
            logoutBtn.style.marginTop = '1rem';
            logoutBtn.style.backgroundColor = '#dc3545';
            
            logoutBtn.addEventListener('click', () => {
                logoutAdmin();
                showStatus('已登出', 'info');
                logoutBtn.remove();
                document.getElementById('loginBtn').textContent = '登入';
            });
            
            document.getElementById('loginForm').appendChild(logoutBtn);
        }
    </script>
</body>
</html>
