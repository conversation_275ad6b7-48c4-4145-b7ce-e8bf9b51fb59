/**
 * 折扣計算器
 * 從 previous_ref/discountCalculator.ts 改寫為 Vanilla JavaScript
 */

/**
 * 計算單個折扣規則的折扣金額
 * @param {Array} cartItems - 購物車項目
 * @param {Object} discount - 折扣規則
 * @returns {Object} 折扣計算結果
 */
export function calculateQuantityTierDiscount(cartItems, discount) {
  let applicableQuantity = 0;
  let applicableSubtotal = 0;

  // 計算適用商品的數量和小計
  cartItems.forEach(item => {
    // 檢查商品是否適用於此折扣
    const isApplicable = !discount.applicable_item_ids?.length || 
                        discount.applicable_item_ids.includes(item.itemId || item.id);
    
    if (isApplicable) {
      applicableQuantity += item.quantity;
      applicableSubtotal += item.subtotal || (item.quantity * item.price);
    }
  });

  // 檢查是否達到門檻
  if (applicableQuantity >= discount.quantity_threshold) {
    const totalDiscount = Math.min(
      applicableQuantity * discount.discount_per_item,
      applicableSubtotal
    );

    return {
      discountId: discount.id,
      discountAmount: totalDiscount,
      discountDetails: {
        name: discount.name,
        description: discount.description,
        applicableQuantity,
        thresholdQuantity: discount.quantity_threshold,
        discountPerItem: discount.discount_per_item,
        totalDiscount,
        applicableSubtotal,
      },
    };
  }

  return {
    discountId: null,
    discountAmount: 0,
    discountDetails: null,
  };
}

/**
 * 計算多個折扣規則中的最佳折扣
 * @param {Array} cartItems - 購物車項目
 * @param {Array} discounts - 折扣規則列表
 * @returns {Object} 最佳折扣計算結果
 */
export function calculateBestDiscount(cartItems, discounts) {
  if (!cartItems.length || !discounts.length) {
    return {
      discountId: null,
      discountAmount: 0,
      discountDetails: null,
    };
  }

  let bestDiscount = {
    discountId: null,
    discountAmount: 0,
    discountDetails: null,
  };

  discounts.forEach(discount => {
    const result = calculateQuantityTierDiscount(cartItems, discount);
    if (result.discountAmount > bestDiscount.discountAmount) {
      bestDiscount = result;
    }
  });

  return bestDiscount;
}

/**
 * 格式化折扣顯示文字
 * @param {Object} result - 折扣計算結果
 * @returns {string} 格式化的折扣說明文字
 */
export function formatDiscountDescription(result) {
  if (!result.discountDetails) {
    return '未符合任何折扣條件';
  }

  const { name, applicableQuantity, thresholdQuantity, discountPerItem, totalDiscount } =
    result.discountDetails;

  return `${name}: 已購買 ${applicableQuantity} 件商品，達到 ${thresholdQuantity} 件門檻，每件折抵 ${discountPerItem} 元，共折抵 ${totalDiscount} 元`;
}

/**
 * 獲取下一個可達成的折扣規則
 * @param {Array} cartItems - 購物車項目
 * @param {Array} discounts - 折扣規則列表
 * @returns {Object|null} 下一個折扣規則資訊
 */
export function getNextAvailableDiscount(cartItems, discounts) {
  if (!cartItems.length || !discounts.length) {
    return null;
  }

  // 計算當前總數量
  const totalQuantity = cartItems.reduce((total, item) => total + item.quantity, 0);

  // 找出所有未達成的折扣規則
  const availableDiscounts = discounts
    .filter(discount => discount.quantity_threshold > totalQuantity)
    .sort((a, b) => a.quantity_threshold - b.quantity_threshold);

  if (availableDiscounts.length === 0) {
    return null;
  }

  const nextDiscount = availableDiscounts[0];
  const remainingQuantity = nextDiscount.quantity_threshold - totalQuantity;

  return {
    discount: nextDiscount,
    remainingQuantity,
    message: `再購買 ${remainingQuantity} 件商品即可享有「${nextDiscount.name}」折扣`
  };
}

/**
 * 檢查折扣規則是否有效
 * @param {Object} discount - 折扣規則
 * @returns {boolean} 是否有效
 */
export function isDiscountValid(discount) {
  if (!discount.active) {
    return false;
  }

  const now = new Date();

  // 檢查開始日期
  if (discount.start_date && new Date(discount.start_date) > now) {
    return false;
  }

  // 檢查結束日期
  if (discount.end_date && new Date(discount.end_date) < now) {
    return false;
  }

  return true;
}

/**
 * 過濾有效的折扣規則
 * @param {Array} discounts - 折扣規則列表
 * @returns {Array} 有效的折扣規則列表
 */
export function filterValidDiscounts(discounts) {
  return discounts.filter(isDiscountValid);
}

/**
 * 計算購物車項目的小計
 * @param {Array} cartItems - 購物車項目
 * @returns {number} 小計金額
 */
export function calculateSubtotal(cartItems) {
  return cartItems.reduce((total, item) => {
    return total + (item.subtotal || (item.quantity * item.price));
  }, 0);
}

/**
 * 計算購物車項目的總數量
 * @param {Array} cartItems - 購物車項目
 * @returns {number} 總數量
 */
export function calculateTotalQuantity(cartItems) {
  return cartItems.reduce((total, item) => total + item.quantity, 0);
}

/**
 * 格式化金額顯示
 * @param {number} amount - 金額
 * @returns {string} 格式化後的金額字串
 */
export function formatAmount(amount) {
  return Math.round(amount).toLocaleString('zh-TW');
}

/**
 * 計算最終金額（小計 - 折扣）
 * @param {Array} cartItems - 購物車項目
 * @param {Array} discounts - 折扣規則列表
 * @returns {Object} 計算結果
 */
export function calculateFinalAmount(cartItems, discounts) {
  const subtotal = calculateSubtotal(cartItems);
  const totalQuantity = calculateTotalQuantity(cartItems);
  const validDiscounts = filterValidDiscounts(discounts);
  const bestDiscount = calculateBestDiscount(cartItems, validDiscounts);
  const finalAmount = subtotal - bestDiscount.discountAmount;

  return {
    subtotal,
    totalQuantity,
    discountAmount: bestDiscount.discountAmount,
    finalAmount: Math.max(0, finalAmount), // 確保最終金額不為負數
    discountDetails: bestDiscount.discountDetails,
    nextDiscount: getNextAvailableDiscount(cartItems, validDiscounts)
  };
}

/**
 * 模擬添加商品後的折扣計算
 * @param {Array} cartItems - 當前購物車項目
 * @param {Object} newItem - 要添加的商品
 * @param {Array} discounts - 折扣規則列表
 * @returns {Object} 添加商品後的計算結果
 */
export function simulateAddItem(cartItems, newItem, discounts) {
  const simulatedCart = [...cartItems];
  
  // 查找是否已存在相同商品
  const existingItemIndex = simulatedCart.findIndex(item => 
    (item.itemId || item.id) === (newItem.itemId || newItem.id)
  );

  if (existingItemIndex >= 0) {
    // 更新現有商品數量
    simulatedCart[existingItemIndex] = {
      ...simulatedCart[existingItemIndex],
      quantity: simulatedCart[existingItemIndex].quantity + newItem.quantity
    };
  } else {
    // 添加新商品
    simulatedCart.push(newItem);
  }

  return calculateFinalAmount(simulatedCart, discounts);
}
