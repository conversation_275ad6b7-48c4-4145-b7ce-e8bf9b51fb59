/**
 * 舊版 TypeScript 日期工具 - 已更新為 JavaScript 版本
 * 新版本位於：public/app/js/utils/dateUtils.js
 *
 * 以下代碼已註解，避免誤導和報錯
 */

/*
// 舊版 TypeScript 代碼 - 已廢棄
import { format } from 'date-fns';
import { zhTW } from 'date-fns/locale';

export const formatDateTime = (date: string | Date): string => {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return format(dateObj, 'yyyy/MM/dd HH:mm:ss', { locale: zhTW });
};

export const formatDate = (date: string | Date): string => {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return format(dateObj, 'yyyy/MM/dd', { locale: zhTW });
};

export const isValidDate = (date: string | Date): boolean => {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return dateObj instanceof Date && !isNaN(dateObj.getTime());
};
*/

// 新版 JavaScript 實現請參考：
// public/app/js/utils/dateUtils.js
// 新版本使用原生 JavaScript Intl API，不依賴 date-fns
