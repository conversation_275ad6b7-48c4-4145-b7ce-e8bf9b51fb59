/**
 * 小森活預購系統 - 預購頁面 JavaScript
 * 負責處理專案資訊載入、商品選擇、數量調整、訂單計算等功能
 * 重構為模組化架構，使用新的服務層和狀態管理
 */

import { fetchProjectWithItems, fetchProjectDiscounts, createOrderWithItems } from '../js/services/supabaseService.js';
import { userInfo, isLoggedIn } from '../js/store/authStore.js';
import { cartItems, addItem, updateItemQuantity, removeItem, clearCart, totalCount, subtotal } from '../js/store/cartStore.js';
import { calculateBestDiscount, calculateFinalAmount, formatAmount } from '../js/utils/discountCalculator.js';
import { formatDateTime, formatDate, toISODateString } from '../js/utils/dateUtils.js';
import { $, $$, createElement, setContent, addClass, removeClass, on, ready } from '../js/utils/domUtils.js';

// 模擬商品資料 (實際使用時會從後端API獲取)
const mockItemsData = [
    {
        id: 'item-001',
        name: '有機蔬菜綜合箱',
        description: '每週精選5-7種當季有機蔬菜，品項會依照產季調整。',
        price: 580,
        maxQuantity: 10
    },
    {
        id: 'item-002',
        name: '有機水果綜合箱',
        description: '每週精選3-5種當季有機水果，品項會依照產季調整。',
        price: 680,
        maxQuantity: 5
    },
    {
        id: 'item-003',
        name: '有機菇菌類組合',
        description: '精選3種有機菇菌類，包含杏鮑菇、香菇、白精靈菇等。',
        price: 320,
        maxQuantity: 8
    },
    {
        id: 'item-004',
        name: '有機葉菜類組合',
        description: '精選4種有機葉菜類，包含小松菜、A菜、菠菜、地瓜葉等。',
        price: 280,
        maxQuantity: 8
    }
];

// 模擬折扣規則 (實際使用時會從後端API獲取)
const mockDiscountRules = [
    { minItems: 3, discountPercentage: 5 },
    { minItems: 5, discountPercentage: 10 },
    { minItems: 10, discountPercentage: 15 }
];

// 全域變數
let cart = {}; // 購物車物件，格式: { itemId: quantity }
let totalItems = 0; // 購物車總數量
let subtotal = 0; // 小計金額
let discount = 0; // 折扣金額
let total = 0; // 總金額

// DOM 載入完成後執行
document.addEventListener('DOMContentLoaded', () => {
    // 載入專案資訊
    loadProjectDetails();

    // 載入商品列表
    loadItems();

    // 初始化訂單摘要區
    updateOrderSummary();

    // 綁定提交訂單按鈕事件
    document.getElementById('submitOrderBtn').addEventListener('click', submitOrder);
});

/**
 * 取得專案狀態顯示文字
 * @param {string} status - 專案狀態
 * @returns {string} 顯示文字
 */
function getStatusDisplay(status) {
    const displays = {
        active: {
            label: '進行中',
            deadline: `預購截止：${formatDateTime(mockProjectData.deadline)}`
        },
        ordering_ended: {
            label: '結束預購',
            deadline: `預計到貨：${formatDateTime(mockProjectData.arrival_date)}`
        },
        arrived: {
            label: '已到貨',
            deadline: '可開始取貨'
        },
        completed: {
            label: '已結束',
            deadline: ''
        }
    };
    return displays[status] || { label: '未知狀態', deadline: '' };
}

/**
 * 格式化日期時間
 * @param {string} dateString - ISO 8601 格式的日期時間字串
 * @returns {string} 格式化後的日期時間
 */
function formatDateTime(dateString) {
    return new Intl.DateTimeFormat('zh-TW', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        hour12: false
    }).format(new Date(dateString));
}

/**
 * 載入專案詳細資訊
 */
function loadProjectDetails() {
    // 填充專案標題
    document.getElementById('projectTitle').textContent = mockProjectData.title;

    // 填充專案狀態和期限
    const statusDisplay = getStatusDisplay(mockProjectData.status);
    const statusElement = document.getElementById('projectStatus');
    const deadlineElement = document.getElementById('projectDeadline');

    statusElement.textContent = statusDisplay.label;
    statusElement.className = `status project-status status-${mockProjectData.status}`;
    deadlineElement.textContent = statusDisplay.deadline;

    // 填充專案描述
    document.getElementById('projectDescription').textContent = mockProjectData.description;

    // 填充專案亮點（最多3個）
    const highlightsContainer = document.getElementById('projectHighlights');
    highlightsContainer.innerHTML = '';

    if (mockProjectData.highlights && mockProjectData.highlights.length > 0) {
        highlightsContainer.style.display = 'flex';
        mockProjectData.highlights.slice(0, 3).forEach(highlight => {
            const highlightElement = document.createElement('div');
            highlightElement.className = 'highlight-item';
            highlightElement.innerHTML = `
                <div class="highlight-icon">${highlight.icon}</div>
                <div class="highlight-text">${highlight.text}</div>
            `;
            highlightsContainer.appendChild(highlightElement);
        });
    } else {
        highlightsContainer.style.display = 'none';
    }

    // 填充專案圖片
    const galleryContainer = document.getElementById('projectGallery');
    galleryContainer.innerHTML = '';

    mockProjectData.gallery.forEach(imageUrl => {
        const imgElement = document.createElement('img');
        imgElement.src = imageUrl;
        imgElement.alt = mockProjectData.title;
        imgElement.className = 'gallery-image';
        galleryContainer.appendChild(imgElement);
    });

    // 根據狀態控制訂購功能
    const itemsSection = document.getElementById('itemsSelectionSection');
    const orderSummarySection = document.getElementById('orderSummarySection');
    const submitBtn = document.getElementById('submitOrderBtn');

    if (mockProjectData.status !== 'active') {
        itemsSection.style.display = 'none';
        orderSummarySection.style.display = 'none';
        submitBtn.disabled = true;
    } else {
        itemsSection.style.display = 'block';
        orderSummarySection.style.display = 'block';
        // submitBtn 的啟用狀態仍由購物車內容決定
    }
}

/**
 * 載入商品列表
 */
function loadItems() {
    const itemsListContainer = document.getElementById('itemsList');
    itemsListContainer.innerHTML = ''; // 清空容器

    mockItemsData.forEach(item => {
        const itemElement = document.createElement('div');
        itemElement.className = 'item-card';
        itemElement.dataset.itemId = item.id;

        itemElement.innerHTML = `
            <div class="item-info">
                <h3 class="item-name">${item.name}</h3>
                <p class="item-description">${item.description}</p>
            </div>
            <div class="item-price">$${formatPrice(item.price)}</div>
            <div class="quantity-control">
                <button class="quantity-btn decrease-qty" aria-label="減少數量" disabled>-</button>
                <input type="number" class="quantity-input" value="0" min="0" max="${item.maxQuantity}"
                       aria-label="數量" data-item-price="${item.price}" data-item-id="${item.id}" data-item-name="${item.name}">
                <button class="quantity-btn increase-qty" aria-label="增加數量">+</button>
            </div>
        `;

        itemsListContainer.appendChild(itemElement);

        // 綁定數量控制按鈕事件
        const quantityInput = itemElement.querySelector('.quantity-input');
        const decreaseBtn = itemElement.querySelector('.decrease-qty');
        const increaseBtn = itemElement.querySelector('.increase-qty');

        // 數量減少按鈕
        decreaseBtn.addEventListener('click', () => {
            if (parseInt(quantityInput.value) > 0) {
                quantityInput.value = parseInt(quantityInput.value) - 1;
                updateQuantity(item.id, parseInt(quantityInput.value), item.name, item.price);
                toggleQuantityButtons(quantityInput, decreaseBtn, increaseBtn);
            }
        });

        // 數量增加按鈕
        increaseBtn.addEventListener('click', () => {
            if (parseInt(quantityInput.value) < item.maxQuantity) {
                quantityInput.value = parseInt(quantityInput.value) + 1;
                updateQuantity(item.id, parseInt(quantityInput.value), item.name, item.price);
                toggleQuantityButtons(quantityInput, decreaseBtn, increaseBtn);
            }
        });

        // 數量輸入框變更事件
        quantityInput.addEventListener('change', () => {
            let value = parseInt(quantityInput.value);

            // 確保數量在有效範圍內
            if (isNaN(value) || value < 0) {
                value = 0;
            } else if (value > item.maxQuantity) {
                value = item.maxQuantity;
            }

            quantityInput.value = value;
            updateQuantity(item.id, value, item.name, item.price);
            toggleQuantityButtons(quantityInput, decreaseBtn, increaseBtn);
        });
    });
}

/**
 * 更新商品數量
 * @param {string} itemId - 商品ID
 * @param {number} quantity - 數量
 * @param {string} itemName - 商品名稱
 * @param {number} itemPrice - 商品價格
 */
function updateQuantity(itemId, quantity, itemName, itemPrice) {
    // 更新購物車
    if (quantity > 0) {
        cart[itemId] = {
            quantity: quantity,
            name: itemName,
            price: itemPrice
        };
    } else {
        delete cart[itemId];
    }

    // 更新訂單摘要
    updateOrderSummary();
}

/**
 * 切換數量控制按鈕狀態
 * @param {HTMLElement} input - 數量輸入框
 * @param {HTMLElement} decreaseBtn - 減少按鈕
 * @param {HTMLElement} increaseBtn - 增加按鈕
 */
function toggleQuantityButtons(input, decreaseBtn, increaseBtn) {
    const value = parseInt(input.value);
    const max = parseInt(input.getAttribute('max'));

    decreaseBtn.disabled = value <= 0;
    increaseBtn.disabled = value >= max;
}

/**
 * 更新訂單摘要區域
 */
function updateOrderSummary() {
    const cartItemsList = document.getElementById('cartItemsList');
    const subtotalAmount = document.getElementById('subtotalAmount');
    const discountAmountRow = document.getElementById('discountAmountRow');
    const discountAmountValue = document.getElementById('discountAmountValue');
    const finalTotalAmount = document.getElementById('finalTotalAmount');
    const discountInfo = document.getElementById('discountInfo');
    const headerCartBadge = document.getElementById('headerCartBadge');
    const submitOrderBtn = document.getElementById('submitOrderBtn');

    // 重新計算總數量和小計
    totalItems = 0;
    subtotal = 0;

    for (const itemId in cart) {
        totalItems += cart[itemId].quantity;
        subtotal += cart[itemId].quantity * cart[itemId].price;
    }

    // 計算折扣
    discount = calculateDiscount(totalItems, subtotal);

    // 計算總金額
    total = subtotal - discount;

    // 更新頂部導航欄的購物車數量
    headerCartBadge.textContent = totalItems;

    // 更新購物車列表
    if (Object.keys(cart).length === 0) {
        cartItemsList.innerHTML = '<p class="cart-empty-message">請從上方選擇商品</p>';
        discountInfo.textContent = '';
        discountAmountRow.style.display = 'none';
    } else {
        cartItemsList.innerHTML = '';

        for (const itemId in cart) {
            const item = cart[itemId];
            const itemElement = document.createElement('div');
            itemElement.className = 'cart-item';
            itemElement.dataset.summaryItemId = itemId;

            itemElement.innerHTML = `
                <div class="cart-item-info">
                    <div class="cart-item-name">${item.name}</div>
                    <div class="item-quantity">(x${item.quantity})</div>
                </div>
                <div class="cart-item-actions">
                    <div class="cart-item-price">$${formatPrice(item.quantity * item.price)}</div>
                    <button class="btn btn-outline btn-sm remove-item-btn" data-item-id="${itemId}">移除</button>
                </div>
            `;

            cartItemsList.appendChild(itemElement);

            // 綁定移除按鈕事件
            const removeBtn = itemElement.querySelector('.remove-item-btn');
            removeBtn.addEventListener('click', () => {
                // 更新商品列表中的數量輸入框
                const quantityInput = document.querySelector(`.quantity-input[data-item-id="${itemId}"]`);
                if (quantityInput) {
                    quantityInput.value = 0;
                    const decreaseBtn = quantityInput.parentElement.querySelector('.decrease-qty');
                    const increaseBtn = quantityInput.parentElement.querySelector('.increase-qty');
                    toggleQuantityButtons(quantityInput, decreaseBtn, increaseBtn);
                }

                // 從購物車中移除
                delete cart[itemId];

                // 更新訂單摘要
                updateOrderSummary();
            });
        }

        // 顯示折扣資訊
        if (discount > 0) {
            const applicableRule = getApplicableDiscountRule(totalItems);
            discountInfo.textContent = `已套用${applicableRule.discountPercentage}%數量折扣`;
            discountAmountRow.style.display = 'flex';
        } else {
            const nextRule = getNextDiscountRule(totalItems);
            if (nextRule) {
                discountInfo.textContent = `再選購${nextRule.minItems - totalItems}件商品即可享有${nextRule.discountPercentage}%折扣`;
            } else {
                discountInfo.textContent = '';
            }
            discountAmountRow.style.display = 'none';
        }
    }

    // 更新金額
    subtotalAmount.textContent = `$${formatPrice(subtotal)}`;
    discountAmountValue.textContent = `-$${formatPrice(discount)}`;
    finalTotalAmount.textContent = `$${formatPrice(total)}`;

    // 更新提交按鈕狀態
    submitOrderBtn.disabled = totalItems === 0;
}

/**
 * 計算折扣金額
 * @param {number} itemCount - 商品總數量
 * @param {number} subtotalAmount - 小計金額
 * @returns {number} 折扣金額
 */
function calculateDiscount(itemCount, subtotalAmount) {
    const applicableRule = getApplicableDiscountRule(itemCount);

    if (applicableRule) {
        return Math.round(subtotalAmount * (applicableRule.discountPercentage / 100));
    }

    return 0;
}

/**
 * 獲取適用的折扣規則
 * @param {number} itemCount - 商品總數量
 * @returns {Object|null} 折扣規則或null
 */
function getApplicableDiscountRule(itemCount) {
    let applicableRule = null;

    // 找出最高折扣規則
    for (const rule of mockDiscountRules) {
        if (itemCount >= rule.minItems) {
            if (!applicableRule || rule.discountPercentage > applicableRule.discountPercentage) {
                applicableRule = rule;
            }
        }
    }

    return applicableRule;
}

/**
 * 獲取下一個折扣規則
 * @param {number} itemCount - 商品總數量
 * @returns {Object|null} 折扣規則或null
 */
function getNextDiscountRule(itemCount) {
    // 按照最小數量排序
    const sortedRules = [...mockDiscountRules].sort((a, b) => a.minItems - b.minItems);

    // 找出下一個可達成的折扣規則
    for (const rule of sortedRules) {
        if (itemCount < rule.minItems) {
            return rule;
        }
    }

    return null;
}

/**
 * 提交訂單
 */
function submitOrder() {
    const submitBtn = document.getElementById('submitOrderBtn');
    const submissionMessage = document.getElementById('submissionMessage');
    const remarks = document.getElementById('remarks').value.trim();
    const pickupDate = document.getElementById('pickupDate').value;

    // 禁用提交按鈕
    submitBtn.disabled = true;
    submitBtn.textContent = '處理中...';

    // 模擬API請求
    setTimeout(() => {
        // 構建訂單資料
        const orderData = {
            projectId: mockProjectData.id,
            items: cart,
            totalItems: totalItems,
            subtotal: subtotal,
            discount: discount,
            total: total,
            remarks: remarks,
            pickupDate: pickupDate,
            createdAt: new Date().toISOString()
        };

        console.log('提交訂單:', orderData);

        // 模擬成功回應
        submissionMessage.textContent = '訂單已成功提交！';
        submissionMessage.className = 'submission-message success';

        // 重置表單
        setTimeout(() => {
            // 清空購物車
            cart = {};

            // 重置所有數量輸入框
            document.querySelectorAll('.quantity-input').forEach(input => {
                input.value = 0;
                const decreaseBtn = input.parentElement.querySelector('.decrease-qty');
                const increaseBtn = input.parentElement.querySelector('.increase-qty');
                toggleQuantityButtons(input, decreaseBtn, increaseBtn);
            });

            // 重置備註和取貨日期
            document.getElementById('remarks').value = '';
            document.getElementById('pickupDate').value = '';

            // 更新訂單摘要
            updateOrderSummary();

            // 重置提交按鈕
            submitBtn.textContent = '確認送出訂單';

            // 清空提交訊息
            setTimeout(() => {
                submissionMessage.textContent = '';
            }, 3000);
        }, 2000);
    }, 1500);
}

/**
 * 格式化價格
 * @param {number} price - 價格
 * @returns {string} 格式化後的價格
 */
function formatPrice(price) {
    return price.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
}

/**
 * 格式化日期
 * @param {string} dateString - 日期字串 (YYYY-MM-DD)
 * @returns {string} 格式化後的日期
 */
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-TW', { year: 'numeric', month: 'long', day: 'numeric' });
}

/**
 * 獲取狀態文字
 * @param {string} status - 狀態代碼
 * @returns {string} 狀態文字
 */
function getStatusText(status) {
    const statusMap = {
        'active': '進行中',
        'pending': '即將開始',
        'closed': '已結束'
    };

    return statusMap[status] || status;
}
