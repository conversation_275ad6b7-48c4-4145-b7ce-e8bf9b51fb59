/**
 * 管理員儀表板頁面 JavaScript 邏輯
 * 負責管理後台的總覽功能
 */

import { fetchActiveProjects, fetchUserOrders } from '../../services/supabaseService.js';
import { userInfo, isAdminLoggedIn, hasAdminPermission } from '../../store/authStore.js';
import { formatDateTime, formatDate, getRelativeTime } from '../../utils/dateUtils.js';
import { formatAmount } from '../../utils/discountCalculator.js';
import { $, $$, createElement, setContent, addClass, removeClass, on, ready, show, hide } from '../../utils/domUtils.js';

// 頁面狀態
let dashboardData = {
  projects: [],
  recentOrders: [],
  stats: {
    totalProjects: 0,
    activeProjects: 0,
    totalOrders: 0,
    totalRevenue: 0
  }
};

/**
 * 初始化管理員儀表板頁面
 */
async function initializeAdminDashboardPage() {
  console.log('Initializing admin dashboard page...');
  
  try {
    // 檢查管理員權限
    if (!checkAdminPermission()) {
      redirectToLogin();
      return;
    }

    // 設置事件監聽器
    setupEventListeners();
    
    // 設置狀態監聽器
    setupStoreListeners();
    
    // 載入儀表板資料
    await loadDashboardData();
    
    console.log('Admin dashboard page initialized successfully');
  } catch (error) {
    console.error('Error initializing admin dashboard page:', error);
    showError('頁面初始化失敗，請重新整理頁面');
  }
}

/**
 * 檢查管理員權限
 * @returns {boolean} 是否有管理員權限
 */
function checkAdminPermission() {
  const user = userInfo.get();
  
  if (!user) {
    console.log('User not logged in');
    return false;
  }
  
  if (!hasAdminPermission()) {
    console.log('User does not have admin permission');
    return false;
  }
  
  return true;
}

/**
 * 重定向到登入頁面
 */
function redirectToLogin() {
  showError('您沒有管理員權限，即將跳轉到登入頁面');
  setTimeout(() => {
    window.location.href = 'login.html';
  }, 2000);
}

/**
 * 設置事件監聽器
 */
function setupEventListeners() {
  // 快速操作按鈕
  const createProjectBtn = $('#createProjectBtn');
  if (createProjectBtn) {
    on(createProjectBtn, 'click', () => {
      window.location.href = 'management/projects.html';
    });
  }

  const manageOrdersBtn = $('#manageOrdersBtn');
  if (manageOrdersBtn) {
    on(manageOrdersBtn, 'click', () => {
      window.location.href = 'management/order-management.html';
    });
  }

  const manageUsersBtn = $('#manageUsersBtn');
  if (manageUsersBtn) {
    on(manageUsersBtn, 'click', () => {
      window.location.href = 'management/user-management.html';
    });
  }

  // 登出按鈕
  const logoutBtn = $('#logoutBtn');
  if (logoutBtn) {
    on(logoutBtn, 'click', handleLogout);
  }

  // 刷新按鈕
  const refreshBtn = $('#refreshBtn');
  if (refreshBtn) {
    on(refreshBtn, 'click', () => {
      loadDashboardData();
    });
  }
}

/**
 * 設置狀態監聽器
 */
function setupStoreListeners() {
  // 監聽用戶登入狀態變化
  userInfo.subscribe((user) => {
    if (!user || !hasAdminPermission()) {
      redirectToLogin();
    } else {
      updateUserInfo(user);
    }
  });
}

/**
 * 載入儀表板資料
 */
async function loadDashboardData() {
  try {
    showLoading(true);
    
    console.log('Loading dashboard data...');
    
    // 並行載入資料
    const [projects, recentOrders] = await Promise.all([
      fetchActiveProjects(),
      fetchRecentOrders()
    ]);
    
    dashboardData.projects = projects;
    dashboardData.recentOrders = recentOrders;
    
    // 計算統計資料
    calculateStats();
    
    // 渲染儀表板
    renderDashboard();
    
    console.log('Dashboard data loaded successfully');
    
  } catch (error) {
    console.error('Error loading dashboard data:', error);
    showError('載入儀表板資料失敗，請稍後再試');
  } finally {
    showLoading(false);
  }
}

/**
 * 獲取最近的訂單
 * @returns {Promise<Array>} 最近的訂單列表
 */
async function fetchRecentOrders() {
  try {
    // 這裡應該調用一個獲取所有訂單的 API
    // 暫時返回空陣列，實際實現時需要添加相應的 API
    console.log('Fetching recent orders...');
    return [];
  } catch (error) {
    console.error('Error fetching recent orders:', error);
    return [];
  }
}

/**
 * 計算統計資料
 */
function calculateStats() {
  const stats = dashboardData.stats;
  
  // 專案統計
  stats.totalProjects = dashboardData.projects.length;
  stats.activeProjects = dashboardData.projects.filter(p => p.project_status === 'active').length;
  
  // 訂單統計
  stats.totalOrders = dashboardData.recentOrders.length;
  stats.totalRevenue = dashboardData.recentOrders.reduce((total, order) => {
    return total + (order.final_amount || 0);
  }, 0);
}

/**
 * 渲染儀表板
 */
function renderDashboard() {
  renderStats();
  renderRecentProjects();
  renderRecentOrders();
  updateLastUpdated();
}

/**
 * 渲染統計卡片
 */
function renderStats() {
  const stats = dashboardData.stats;
  
  // 總專案數
  const totalProjectsElement = $('#totalProjects');
  if (totalProjectsElement) {
    setContent(totalProjectsElement, stats.totalProjects.toString());
  }
  
  // 進行中專案數
  const activeProjectsElement = $('#activeProjects');
  if (activeProjectsElement) {
    setContent(activeProjectsElement, stats.activeProjects.toString());
  }
  
  // 總訂單數
  const totalOrdersElement = $('#totalOrders');
  if (totalOrdersElement) {
    setContent(totalOrdersElement, stats.totalOrders.toString());
  }
  
  // 總營收
  const totalRevenueElement = $('#totalRevenue');
  if (totalRevenueElement) {
    setContent(totalRevenueElement, `$${formatAmount(stats.totalRevenue)}`);
  }
}

/**
 * 渲染最近專案
 */
function renderRecentProjects() {
  const container = $('#recentProjectsList');
  if (!container) return;

  container.innerHTML = '';

  if (dashboardData.projects.length === 0) {
    const emptyMessage = createElement('div', {
      className: 'empty-message'
    }, '目前沒有專案');
    
    container.appendChild(emptyMessage);
    return;
  }

  // 顯示最近的 5 個專案
  const recentProjects = dashboardData.projects.slice(0, 5);
  
  recentProjects.forEach(project => {
    const projectElement = createElement('div', {
      className: 'project-item'
    }, [
      createElement('div', { className: 'project-info' }, [
        createElement('div', { className: 'project-name' }, project.name),
        createElement('div', { className: 'project-status' }, getProjectStatusText(project.project_status))
      ]),
      createElement('div', { className: 'project-date' }, formatDate(project.created_at))
    ]);
    
    container.appendChild(projectElement);
  });
}

/**
 * 渲染最近訂單
 */
function renderRecentOrders() {
  const container = $('#recentOrdersList');
  if (!container) return;

  container.innerHTML = '';

  if (dashboardData.recentOrders.length === 0) {
    const emptyMessage = createElement('div', {
      className: 'empty-message'
    }, '目前沒有訂單');
    
    container.appendChild(emptyMessage);
    return;
  }

  // 顯示最近的 5 個訂單
  const recentOrders = dashboardData.recentOrders.slice(0, 5);
  
  recentOrders.forEach(order => {
    const orderElement = createElement('div', {
      className: 'order-item'
    }, [
      createElement('div', { className: 'order-info' }, [
        createElement('div', { className: 'order-number' }, order.order_number),
        createElement('div', { className: 'order-amount' }, `$${formatAmount(order.final_amount)}`)
      ]),
      createElement('div', { className: 'order-date' }, getRelativeTime(order.created_at))
    ]);
    
    container.appendChild(orderElement);
  });
}

/**
 * 更新最後更新時間
 */
function updateLastUpdated() {
  const lastUpdatedElement = $('#lastUpdated');
  if (lastUpdatedElement) {
    setContent(lastUpdatedElement, formatDateTime(new Date()));
  }
}

/**
 * 更新用戶資訊顯示
 * @param {Object} user - 用戶資訊
 */
function updateUserInfo(user) {
  const userNameElement = $('#adminUserName');
  if (userNameElement) {
    setContent(userNameElement, user.displayName || '管理員');
  }
}

/**
 * 獲取專案狀態文字
 * @param {string} status - 專案狀態
 * @returns {string} 狀態文字
 */
function getProjectStatusText(status) {
  switch (status) {
    case 'active':
      return '進行中';
    case 'ordering_ended':
      return '結束預購';
    case 'arrived':
      return '已到貨';
    case 'completed':
      return '已結束';
    default:
      return '未知狀態';
  }
}

/**
 * 處理登出
 */
function handleLogout() {
  try {
    console.log('Admin logging out...');
    
    // 觸發登出
    import('../../services/liffService.js').then(({ logout }) => {
      logout();
    });
    
  } catch (error) {
    console.error('Logout failed:', error);
    showError('登出失敗，請稍後再試');
  }
}

/**
 * 顯示載入狀態
 * @param {boolean} show - 是否顯示載入狀態
 */
function showLoading(show) {
  const loadingElement = $('#dashboardLoading');
  if (loadingElement) {
    if (show) {
      show(loadingElement);
    } else {
      hide(loadingElement);
    }
  }
}

/**
 * 顯示錯誤訊息
 * @param {string} message - 錯誤訊息
 */
function showError(message) {
  // 創建錯誤提示
  const errorDiv = createElement('div', {
    className: 'error-message',
    style: `
      position: fixed;
      top: 20px;
      left: 50%;
      transform: translateX(-50%);
      background: #f44336;
      color: white;
      padding: 12px 24px;
      border-radius: 4px;
      z-index: 10000;
      font-size: 14px;
    `
  }, message);
  
  document.body.appendChild(errorDiv);
  
  // 5秒後自動移除
  setTimeout(() => {
    if (errorDiv.parentNode) {
      errorDiv.parentNode.removeChild(errorDiv);
    }
  }, 5000);
}

// 當 DOM 載入完成時初始化管理員儀表板頁面
ready(() => {
  // 檢查是否為管理員儀表板頁面
  const isAdminDashboardPage = window.location.pathname.includes('admin/dashboard.html');
  
  if (isAdminDashboardPage) {
    initializeAdminDashboardPage();
  }
});

// 導出函數供其他模組使用
export { initializeAdminDashboardPage };
