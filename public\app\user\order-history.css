/* --- Order History Page Specific Styles --- */

.page-order-history .main-content .container {
    padding-top: var(--space-lg);
    padding-bottom: var(--space-lg);
}

.page-title {
    font-size: var(--font-size-xxxl); /* Use variable */
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: var(--space-lg);
}

/* Filter Container */
.filter-container {
    background-color: var(--bg-white);
    border-radius: var(--radius-lg); /* Consistent radius */
    box-shadow: var(--shadow-md); /* Use variable */
    padding: var(--space-lg);
    margin-bottom: var(--space-xl); /* Increase margin */
}

.filter-title {
    font-size: var(--font-size-xl); /* Use variable */
    font-weight: 600;
    margin-bottom: var(--space-lg); /* Increase margin */
    padding-bottom: var(--space-md); /* Space before border */
    border-bottom: 1px solid var(--border-light); /* Use variable */
}

.filter-form {
    display: grid; /* Use grid for better alignment */
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); /* Responsive columns */
    gap: var(--space-lg); /* Consistent gap */
}

.filter-group {
    /* Structure provided by grid */
}

.filter-label {
    /* Inherits .form-label */
}

.filter-input,
.filter-select {
    /* Inherits .form-input, .form-select */
}

.filter-actions {
    display: flex;
    justify-content: flex-end;
    margin-top: var(--space-lg);
    padding-top: var(--space-lg); /* Space after form fields */
    border-top: 1px solid var(--border-light);
}

/* Orders List */
.orders-container {
    display: flex;
    flex-direction: column;
    gap: var(--space-lg); /* Consistent gap */
}

.order-card {
    /* Inherits base .card styles */
    border-radius: var(--radius-lg); /* Ensure consistency */
}

.order-header {
    padding: var(--space-md) var(--space-lg); /* Consistent padding */
    border-bottom: 1px solid var(--border-light); /* Use variable */
    display: flex;
    flex-wrap: wrap; /* Allow wrapping on small screens */
    justify-content: space-between;
    align-items: center;
    gap: var(--space-md); /* Space between elements */
}

.order-id-date {
    display: flex;
    flex-direction: column;
    gap: var(--space-xs); /* Small gap between ID and date */
}

.order-id {
    font-weight: 600;
    font-size: var(--font-size-base); /* Adjust size */
}

.order-date {
    font-size: var(--font-size-sm);
    color: var(--text-medium); /* Use variable */
}

.order-status {
    /* Inherits .status styles */
    /* Specific status colors defined in shared-styles */
    flex-shrink: 0; /* Prevent shrinking */
}

.order-body {
    padding: var(--space-lg); /* Consistent padding */
}

.order-project {
    margin-bottom: var(--space-md);
    display: flex;
    align-items: center;
    gap: var(--space-md); /* Consistent gap */
    padding-bottom: var(--space-md);
    border-bottom: 1px solid var(--border-light);
}

.project-image-container { /* Container for image */
    width: 60px;
    height: 60px;
    border-radius: var(--radius-md); /* Use variable */
    overflow: hidden;
    background-color: var(--bg-medium);
    flex-shrink: 0;
}

.project-image { /* Image itself */
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.project-info {
    /* Flex item, grows to fill space */
}

.project-name {
    font-weight: 600;
    font-size: var(--font-size-md); /* Adjust size */
    margin-bottom: var(--space-xs);
}

.order-items {
    margin-bottom: var(--space-lg);
}

.order-item {
    display: flex;
    flex-wrap: wrap; /* Allow wrap */
    justify-content: space-between;
    padding: var(--space-sm) 0; /* Adjust padding */
    color: var(--text-medium);
    font-size: var(--font-size-base); /* Adjust size */
    border-bottom: 1px dashed var(--border-light); /* Dashed separator */
}
.order-item:last-child {
    border-bottom: none;
}

.item-name-qty {
    display: flex;
    gap: var(--space-sm); /* Use variable */
    margin-right: var(--space-md); /* Ensure space before price */
}

.item-qty {
    color: var(--text-light); /* Lighter color for quantity */
}

.item-price {
    font-weight: 500; /* Normal weight */
    color: var(--text-dark);
    white-space: nowrap; /* Prevent price wrapping */
}

.order-summary {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: var(--space-sm); /* Consistent gap */
    padding-top: var(--space-md);
    margin-top: var(--space-md);
    border-top: 1px solid var(--border-light);
}

.summary-row {
    display: flex;
    justify-content: space-between; /* Ensure space between label/value */
    width: 100%; /* Take full width */
    max-width: 300px; /* Limit width for better alignment */
    gap: var(--space-md);
    font-size: var(--font-size-base); /* Adjust size */
}

.summary-label {
    color: var(--text-medium); /* Use variable */
    text-align: left;
}

.summary-value {
    font-weight: 500;
    min-width: 80px; /* Adjust min-width */
    text-align: right;
    color: var(--text-dark);
}

.total-row {
    font-size: var(--font-size-md); /* Larger total */
    font-weight: 600;
    margin-top: var(--space-xs); /* Small space before total */
}

.total-row .summary-label,
.total-row .summary-value {
    color: var(--text-dark); /* Darker total */
}

.order-footer {
    padding: var(--space-md) var(--space-lg); /* Consistent padding */
    border-top: 1px solid var(--border-light);
    display: flex;
    justify-content: flex-end;
    gap: var(--space-md); /* Consistent gap */
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .filter-form {
        grid-template-columns: 1fr; /* Stack filters on small screens */
    }

    .order-header {
        flex-direction: column;
        align-items: flex-start; /* Align items left */
    }

    .order-footer .btn {
        flex-grow: 1; /* Make buttons full width */
    }

    .summary-row {
        max-width: none; /* Allow summary to take full width */
    }
}