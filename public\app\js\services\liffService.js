/**
 * LIFF 服務層
 * 封裝 LINE LIFF SDK 的初始化和常用操作
 */

import { setUserInfo, clearUserInfo, setLiffInitialized } from '../store/authStore.js';
import { upsertUser } from './supabaseService.js';

// LIFF ID 從環境變數獲取
const LIFF_ID = import.meta.env.VITE_LIFF_ID;

if (!LIFF_ID) {
  console.error('Missing LIFF_ID environment variable');
}

/**
 * 初始化 LIFF
 * @returns {Promise<boolean>} 初始化是否成功
 */
export async function initializeLiff() {
  try {
    // 檢查 LIFF SDK 是否已載入
    if (typeof window.liff === 'undefined') {
      console.error('LIFF SDK not loaded');
      return false;
    }

    // 初始化 LIFF
    await window.liff.init({ liffId: LIFF_ID });
    
    console.log('LIFF initialized successfully');
    setLiffInitialized(true);

    // 檢查登入狀態
    if (window.liff.isLoggedIn()) {
      await handleUserLogin();
    }

    return true;
  } catch (error) {
    console.error('LIFF initialization failed:', error);
    setLiffInitialized(false);
    return false;
  }
}

/**
 * 處理用戶登入
 */
async function handleUserLogin() {
  try {
    // 獲取用戶資料
    const profile = await window.liff.getProfile();
    
    // 將用戶資料存入資料庫
    const userData = await upsertUser(profile);
    
    // 更新認證狀態
    setUserInfo({
      id: userData.id,
      displayName: userData.display_name,
      pictureUrl: userData.picture_url,
      role: userData.role,
      communityNickname: userData.community_nickname,
      lastLoginAt: userData.last_login_at
    });

    console.log('User logged in:', userData.display_name);
  } catch (error) {
    console.error('Error handling user login:', error);
    clearUserInfo();
  }
}

/**
 * 用戶登入
 */
export function login() {
  try {
    if (typeof window.liff === 'undefined') {
      console.error('LIFF SDK not loaded');
      return;
    }

    if (!window.liff.isLoggedIn()) {
      window.liff.login();
    } else {
      console.log('User already logged in');
    }
  } catch (error) {
    console.error('Login failed:', error);
  }
}

/**
 * 用戶登出
 */
export function logout() {
  try {
    if (typeof window.liff === 'undefined') {
      console.error('LIFF SDK not loaded');
      return;
    }

    // 清除本地狀態
    clearUserInfo();
    
    // LIFF 登出
    if (window.liff.isLoggedIn()) {
      window.liff.logout();
    }

    console.log('User logged out');
  } catch (error) {
    console.error('Logout failed:', error);
  }
}

/**
 * 檢查是否已登入
 * @returns {boolean} 是否已登入
 */
export function isLoggedIn() {
  try {
    if (typeof window.liff === 'undefined') {
      return false;
    }
    return window.liff.isLoggedIn();
  } catch (error) {
    console.error('Error checking login status:', error);
    return false;
  }
}

/**
 * 獲取用戶資料
 * @returns {Promise<Object|null>} 用戶資料
 */
export async function getProfile() {
  try {
    if (typeof window.liff === 'undefined') {
      console.error('LIFF SDK not loaded');
      return null;
    }

    if (!window.liff.isLoggedIn()) {
      console.log('User not logged in');
      return null;
    }

    return await window.liff.getProfile();
  } catch (error) {
    console.error('Error getting profile:', error);
    return null;
  }
}

/**
 * 檢查 LIFF 是否在 LINE 應用內運行
 * @returns {boolean} 是否在 LINE 應用內
 */
export function isInClient() {
  try {
    if (typeof window.liff === 'undefined') {
      return false;
    }
    return window.liff.isInClient();
  } catch (error) {
    console.error('Error checking if in client:', error);
    return false;
  }
}

/**
 * 關閉 LIFF 視窗
 */
export function closeWindow() {
  try {
    if (typeof window.liff === 'undefined') {
      console.error('LIFF SDK not loaded');
      return;
    }

    if (window.liff.isInClient()) {
      window.liff.closeWindow();
    } else {
      console.log('Not in LINE client, cannot close window');
    }
  } catch (error) {
    console.error('Error closing window:', error);
  }
}

/**
 * 發送訊息到 LINE 聊天室
 * @param {Array} messages - 訊息陣列
 */
export function sendMessages(messages) {
  try {
    if (typeof window.liff === 'undefined') {
      console.error('LIFF SDK not loaded');
      return;
    }

    if (window.liff.isApiAvailable('sendMessages')) {
      window.liff.sendMessages(messages);
    } else {
      console.log('sendMessages API not available');
    }
  } catch (error) {
    console.error('Error sending messages:', error);
  }
}

/**
 * 分享目標選擇器
 * @param {Array} messages - 訊息陣列
 */
export function shareTargetPicker(messages) {
  try {
    if (typeof window.liff === 'undefined') {
      console.error('LIFF SDK not loaded');
      return;
    }

    if (window.liff.isApiAvailable('shareTargetPicker')) {
      window.liff.shareTargetPicker(messages);
    } else {
      console.log('shareTargetPicker API not available');
    }
  } catch (error) {
    console.error('Error sharing via target picker:', error);
  }
}

/**
 * 獲取 LIFF 環境資訊
 * @returns {Object} 環境資訊
 */
export function getEnvironmentInfo() {
  try {
    if (typeof window.liff === 'undefined') {
      return { error: 'LIFF SDK not loaded' };
    }

    return {
      isLoggedIn: window.liff.isLoggedIn(),
      isInClient: window.liff.isInClient(),
      os: window.liff.getOS(),
      version: window.liff.getVersion(),
      language: window.liff.getLanguage(),
      lineVersion: window.liff.getLineVersion()
    };
  } catch (error) {
    console.error('Error getting environment info:', error);
    return { error: error.message };
  }
}
