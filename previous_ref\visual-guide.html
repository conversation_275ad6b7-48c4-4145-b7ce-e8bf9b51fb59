<!DOCTYPE html>
<html lang="zh-TW">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>小森活預購系統 - 視覺設計指南</title>
  <style>
    /* 基本設置 */
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
      font-family: 'Noto Sans TC', 'Roboto', sans-serif;
    }
    
    body {
      background-color: #FDFDFD;
      color: #333333;
      line-height: 1.6;
      font-size: 16px;
    }
    
    header {
      background-color: #57AC5A;
      color: white;
      padding: 2rem;
      text-align: center;
      box-shadow: 0 4px 12px rgba(87, 172, 90, 0.15);
    }
    
    h1 {
      font-size: 2rem;
      margin-bottom: 1rem;
      font-weight: 700;
    }
    
    .subtitle {
      font-size: 1.2rem;
      opacity: 0.9;
    }
    
    .container {
      max-width: 980px;
      margin: 2rem auto;
      padding: 0 1.5rem;
    }
    
    section {
      background-color: white;
      border-radius: 12px;
      padding: 2rem;
      margin-bottom: 2rem;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    }
    
    h2 {
      color: #57AC5A;
      font-size: 1.5rem;
      margin-bottom: 1.5rem;
      padding-bottom: 0.5rem;
      border-bottom: 2px solid #EAEFE9;
    }
    
    h3 {
      font-size: 1.3rem;
      margin: 1.5rem 0 1rem;
      color: #444;
    }
    
    p {
      margin-bottom: 1rem;
      color: #555;
    }
    
    /* 配色方案展示 */
    .color-palette {
      display: flex;
      flex-wrap: wrap;
      gap: 1.5rem;
      margin: 1.5rem 0;
    }
    
    .color-card {
      flex: 1 1 180px;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    }
    
    .color-preview {
      height: 100px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-weight: 600;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    }
    
    .color-info {
      padding: 1rem;
      background-color: white;
      font-size: 0.9rem;
    }
    
    .color-name {
      font-weight: 600;
      margin-bottom: 0.5rem;
    }
    
    .color-hex {
      font-family: 'Roboto Mono', monospace;
      color: #666;
    }
    
    .color-desc {
      margin-top: 0.5rem;
      font-size: 0.85rem;
      color: #777;
    }
    
    /* 元素展示 */
    .button-row {
      display: flex;
      flex-wrap: wrap;
      gap: 1rem;
      margin: 1rem 0;
    }
    
    .btn {
      padding: 0.75rem 1.5rem;
      border-radius: 8px;
      font-weight: 500;
      border: none;
      cursor: pointer;
      transition: all 0.2s ease;
      font-size: 1rem;
    }
    
    .btn-primary {
      background-color: #57AC5A;
      color: white;
      box-shadow: 0 2px 6px rgba(87, 172, 90, 0.2);
    }
    
    .btn-secondary {
      background-color: #FFC75A;
      color: #704800;
      box-shadow: 0 2px 6px rgba(255, 199, 90, 0.2);
    }

    .btn-outline {
      background-color: transparent;
      color: #57AC5A;
      border: 2px solid #57AC5A;
    }
    
    .btn-text {
      background-color: transparent;
      color: #57AC5A;
      padding: 0.75rem 1rem;
    }
    
    /* 卡片展示 */
    .card-showcase {
      display: flex;
      flex-wrap: wrap;
      gap: 1.5rem;
      margin: 1.5rem 0;
    }
    
    .card {
      flex: 1 1 300px;
      border-radius: 12px;
      overflow: hidden;
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.06);
      transition: transform 0.2s ease, box-shadow 0.2s ease;
    }
    
    .card:hover {
      transform: translateY(-3px);
      box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
    }
    
    .card-img {
      height: 180px;
      background-color: #f0f0f0;
      overflow: hidden;
    }
    
    .card-img img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      transition: transform 0.4s ease;
    }
    
    .card-content {
      padding: 1.5rem;
    }
    
    .card-title {
      font-size: 1.2rem;
      font-weight: 600;
      margin-bottom: 0.5rem;
      color: #333;
    }
    
    .card-status {
      display: inline-block;
      padding: 0.3rem 0.8rem;
      border-radius: 100px;
      font-size: 0.75rem;
      font-weight: 600;
      margin-bottom: 0.75rem;
    }
    
    .status-active {
      background-color: rgba(87, 172, 90, 0.1);
      color: #57AC5A;
    }
    
    .card-text {
      color: #666;
      font-size: 0.95rem;
      margin-bottom: 1rem;
    }
    
    .card-footer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-top: 1px solid #f0f0f0;
      padding-top: 1rem;
      margin-top: 0.5rem;
    }
    
    .card-price {
      font-weight: 600;
      color: #FFC75A;
      font-size: 1.2rem;
    }
    
    /* 字體展示 */
    .typography {
      margin: 2rem 0;
    }
    
    .font-sample {
      margin-bottom: 1.5rem;
    }
    
    .font-title {
      font-size: 1rem;
      color: #666;
      margin-bottom: 0.5rem;
    }
    
    .h1-sample {
      font-size: 2.5rem;
      font-weight: 700;
      line-height: 1.2;
      margin-bottom: 0.5rem;
    }
    
    .h3-sample {
      font-size: 1.5rem;
      font-weight: 600;
      line-height: 1.4;
      margin-bottom: 0.5rem;
    }
    
    .body-sample {
      font-size: 1rem;
      font-weight: 400;
      line-height: 1.6;
      margin-bottom: 0.5rem;
    }
    
    .small-sample {
      font-size: 0.875rem;
      font-weight: 400;
      line-height: 1.6;
      color: #777;
    }
    
    /* 圖標展示 */
    .icon-grid {
      display: flex;
      flex-wrap: wrap;
      gap: 1rem;
      margin: 1.5rem 0;
    }
    
    .icon-box {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      padding: 0.5rem 1rem;
      border-radius: 8px;
      background-color: #f9f9f9;
    }
    
    .icon-box span {
      color: #57AC5A;
      font-size: 1.5rem;
    }
    
    .icon-name {
      font-size: 0.9rem;
      color: #666;
    }
    
    /* 徽章和標籤 */
    .badges {
      display: flex;
      flex-wrap: wrap;
      gap: 0.5rem;
      margin: 1rem 0;
    }
    
    .badge {
      padding: 0.25rem 0.75rem;
      border-radius: 100px;
      font-size: 0.75rem;
      font-weight: 600;
    }
    
    .badge-primary {
      background-color: rgba(87, 172, 90, 0.1);
      color: #57AC5A;
    }
    
    .badge-secondary {
      background-color: rgba(255, 199, 90, 0.1);
      color: #FFC75A;
    }
    
    .badge-neutral {
      background-color: #f0f0f0;
      color: #666;
    }
    
    /* 底部導航 */
    .bottom-nav {
      display: flex;
      background-color: white;
      padding: 1rem 0;
      border-radius: 8px;
      box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
      margin-top: 2rem;
    }
    
    .nav-item {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      text-decoration: none;
      color: #888;
      gap: 0.3rem;
    }
    
    .nav-item.active {
      color: #57AC5A;
    }
    
    .nav-icon {
      font-size: 1.25rem;
    }
    
    .nav-label {
      font-size: 0.75rem;
      font-weight: 500;
    }
  </style>
</head>
<body>
  <header>
    <h1>小森活預購系統 - 視覺設計指南</h1>
    <div class="subtitle">為使用者打造溫暖、自然且賞心悅目的購物體驗</div>
  </header>
  
  <div class="container">
    <section id="overview">
      <h2>設計概述</h2>
      <p>小森活預購系統的視覺風格強調「自然」、「有機」和「溫暖」的感覺，傳達小型社群預購的友善氛圍。UI設計簡潔明快，以用戶體驗為中心，確保手機端的操作流暢且直觀。</p>
      
      <p>整體設計基調：</p>
      <ul>
        <li><strong>自然清新</strong>：使用綠色系為主色調，象徵自然與健康</li>
        <li><strong>溫暖友善</strong>：柔和的弧度和溫暖的黃色強調</li>
        <li><strong>簡潔易用</strong>：減少視覺雜訊，突出重要操作</li>
        <li><strong>現代感</strong>：運用適當的陰影、漸變和微妙動效</li>
      </ul>
    </section>
    
    <section id="colors">
      <h2>配色方案</h2>
      
      <h3>主要配色</h3>
      <div class="color-palette">
        <div class="color-card">
          <div class="color-preview" style="background-color: #57AC5A;">#57AC5A</div>
          <div class="color-info">
            <div class="color-name">主要綠色</div>
            <div class="color-hex">#57AC5A</div>
            <div class="color-desc">按鈕、重點標題、强調元素</div>
          </div>
        </div>
        
        <div class="color-card">
          <div class="color-preview" style="background-color: #FFC75A; color: #704800;">#FFC75A</div>
          <div class="color-info">
            <div class="color-name">溫暖黃色</div>
            <div class="color-hex">#FFC75A</div>
            <div class="color-desc">價格標籤、次要按鈕、提示</div>
          </div>
        </div>
        
        <div class="color-card">
          <div class="color-preview" style="background-color: #FDFDFD; color: #333;">#FDFDFD</div>
          <div class="color-info">
            <div class="color-name">背景色</div>
            <div class="color-hex">#FDFDFD</div>
            <div class="color-desc">頁面背景、白色元素</div>
          </div>
        </div>
      </div>
      
      <h3>輔助配色</h3>
      <div class="color-palette">
        <div class="color-card">
          <div class="color-preview" style="background-color: #333333;">#333333</div>
          <div class="color-info">
            <div class="color-name">主要文字色</div>
            <div class="color-hex">#333333</div>
            <div class="color-desc">標題、重要文字</div>
          </div>
        </div>
        
        <div class="color-card">
          <div class="color-preview" style="background-color: #666666;">#666666</div>
          <div class="color-info">
            <div class="color-name">次要文字色</div>
            <div class="color-hex">#666666</div>
            <div class="color-desc">說明文字、次要內容</div>
          </div>
        </div>
        
        <div class="color-card">
          <div class="color-preview" style="background-color: #EAEFE9;">#EAEFE9</div>
          <div class="color-info">
            <div class="color-name">淺綠色</div>
            <div class="color-hex">#EAEFE9</div>
            <div class="color-desc">背景、分隔條、次要元素</div>
          </div>
        </div>
      </div>
    </section>
    
    <section id="typography">
      <h2>字體與排版</h2>
      
      <div class="typography">
        <div class="font-sample">
          <div class="font-title">主標題</div>
          <div class="h1-sample">小森活預購系統</div>
          <div class="small-sample">Font: Noto Sans TC, 24-28px, 700</div>
        </div>
        
        <div class="font-sample">
          <div class="font-title">卡片標題</div>
          <div class="h3-sample">有機蔬果預購</div>
          <div class="small-sample">Font: Noto Sans TC, 18-20px, 600</div>
        </div>
        
        <div class="font-sample">
          <div class="font-title">正文</div>
          <div class="body-sample">每週產地直送新鮮有機蔬果，支持本地小農，安全有保障。</div>
          <div class="small-sample">Font: Noto Sans TC, 16px, 400</div>
        </div>
      </div>
    </section>
    
    <section id="elements">
      <h2>基本元素</h2>
      
      <h3>按鈕樣式</h3>
      <div class="button-row">
        <button class="btn btn-primary">主要按鈕</button>
        <button class="btn btn-secondary">次要按鈕</button>
        <button class="btn btn-outline">邊框按鈕</button>
        <button class="btn btn-text">文字按鈕</button>
      </div>
      
      <h3>圖標與標籤</h3>
      <div class="icon-grid">
        <div class="icon-box">
          <span>&#x1F6D2;</span>
          <div class="icon-name">購物車</div>
        </div>
        
        <div class="icon-box">
          <span>&#x1F3E0;</span>
          <div class="icon-name">首頁</div>
        </div>
        
        <div class="icon-box">
          <span>&#x1F464;</span>
          <div class="icon-name">用戶</div>
        </div>
        
        <div class="icon-box">
          <span>&#x1F4C3;</span>
          <div class="icon-name">訂單</div>
        </div>
      </div>
      
      <div class="badges">
        <span class="badge badge-primary">進行中</span>
        <span class="badge badge-secondary">特價</span>
        <span class="badge badge-neutral">已結束</span>
      </div>
    </section>
    
    <section id="components">
      <h2>組件示例</h2>
      
      <h3>卡片設計</h3>
      <div class="card-showcase">
        <div class="card">
          <div class="card-img">
            <img src="/api/placeholder/400/180" alt="有機蔬果預購">
          </div>
          <div class="card-content">
            <span class="card-status status-active">進行中</span>
            <h3 class="card-title">有機蔬果預購</h3>
            <p class="card-text">每週產地直送新鮮有機蔬果，支持本地小農，安全有保障。</p>
            <div class="card-footer">
              <div class="card-price">$450起</div>
              <button class="btn btn-outline">查看詳情</button>
            </div>
          </div>
        </div>
      </div>
      
      <h3>底部導航</h3>
      <div class="bottom-nav">
        <div class="nav-item active">
          <div class="nav-icon">&#x1F3E0;</div>
          <span class="nav-label">首頁</span>
        </div>
        
        <div class="nav-item">
          <div class="nav-icon">&#x1F4C3;</div>
          <span class="nav-label">訂單</span>
        </div>
        
        <div class="nav-item">
          <div class="nav-icon">&#x1F464;</div>
          <span class="nav-label">我的</span>
        </div>
      </div>
    </section>
  </div>
</body>
</html>