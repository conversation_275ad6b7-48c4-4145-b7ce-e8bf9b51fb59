# 資料庫行級安全性 (Row Level Security) 設定文檔

本文檔詳細說明了系統中各個資料表的行級安全性 (RLS) 設定。

## 總覽

系統中的資料表分為以下幾類：
1. 核心業務表格（已啟用 RLS）
2. 關聯映射表格（已啟用 RLS）
3. 系統表格（全部已啟用 RLS）

## 管理員權限檢查

系統使用統一的 `is_admin()` 函數來檢查管理員權限。此函數：
- 檢查當前用戶是否為管理員
- 返回布林值（true/false）
- 使用 SECURITY DEFINER 確保安全性
- 用於所有需要管理員權限的政策

## 安全性原則

1. 角色使用規範
   - authenticated：用於所有需要用戶登入的操作（包括管理員操作）
   - public：僅用於確實需要匿名訪問的 SELECT 操作，且僅限於活躍專案相關資訊
   - anon：不再使用，改為適當限制的 public 政策

2. 狀態檢查
   - 訂單更新僅允許在 'Pending' 狀態
   - 訂單項目更新需檢查關聯訂單狀態
   - 公開訪問僅限於活躍狀態的專案相關資訊

## 詳細設定

### 系統表格 (System Tables)

#### ID 序列 (id_sequences)

| 政策名稱 | 操作 | 角色 | 條件 |
|---------|------|------|------|
| admin_manage_sequences | 全部 | authenticated | is_admin() |

#### 專案狀態日誌 (project_status_logs)

| 政策名稱 | 操作 | 角色 | 條件 |
|---------|------|------|------|
| admin_manage_status_logs | 全部 | authenticated | is_admin() |
| owner_view_status_logs | 查詢 | authenticated | 專案擁有者匹配 |

### 使用者管理 (users)

| 政策名稱 | 操作 | 角色 | 條件 |
|---------|------|------|------|
| admin_manage_users | 全部 | authenticated | is_admin() |
| allow_create_user | 新增 | authenticated | true |
| user_select | 查詢 | authenticated | 用戶ID匹配 |
| user_update | 更新 | authenticated | 用戶ID匹配 |

### 專案管理 (projects)

| 政策名稱 | 操作 | 角色 | 條件 |
|---------|------|------|------|
| admin_manage_projects | 全部 | authenticated | is_admin() |
| user_view_all_active_projects | 查詢 | authenticated | 活躍專案 OR 專案擁有者 OR 訂單相關用戶 |
| user_manage_own_projects | 全部 | authenticated | auth.uid()::text = owner_id |

注意：
1. 專案查看權限的具體實現：
   ```sql
   project_status = 'Active'  -- 活躍專案可被所有已認證用戶查看
   OR auth.uid()::text = owner_id  -- 專案擁有者可查看自己的專案（任何狀態）
   OR EXISTS (  -- 有相關訂單的用戶可查看專案（任何狀態）
       SELECT 1 FROM orders 
       WHERE orders.project_id = projects.id 
       AND orders.user_id = auth.uid()::text
   )
   ```

2. 折扣和商品的公開查看權限實現：
   ```sql
   -- public_view_active_discounts 的實現
   EXISTS (
       SELECT 1 
       FROM public.discount_project_mappings dpm
       JOIN public.projects p ON p.id = dpm.project_id
       WHERE dpm.discount_id = discounts.id
       AND p.project_status = 'Active'
   )

   -- public_view_active_items 的實現
   EXISTS (
       SELECT 1 
       FROM public.projects p
       WHERE items.project_id = p.id
       AND p.project_status = 'Active'
   )
   ```

3. 折扣專案映射的查看權限實現：
   ```sql
   -- user_view_mapping 的實現
   EXISTS (
       SELECT 1 FROM projects p 
       WHERE p.id = project_id 
       AND (
           p.owner_id = auth.uid()::text  -- 專案擁有者
           OR EXISTS (
               SELECT 1 FROM orders o  -- 訂單相關用戶
               WHERE o.project_id = p.id 
               AND o.user_id = auth.uid()::text
           )
       )
   )
   ```

### 折扣管理 (discounts)

| 政策名稱 | 操作 | 角色 | 條件 |
|---------|------|------|------|
| admin_manage_discounts | 全部 | authenticated | is_admin() |
| authenticated_view_discounts | 查詢 | authenticated | true |
| public_view_active_discounts | 查詢 | public | 僅活躍專案相關折扣 |

### 折扣專案映射 (discount_project_mappings)

| 政策名稱 | 操作 | 角色 | 條件 |
|---------|------|------|------|
| admin_manage_mappings | 全部 | authenticated | is_admin() |
| user_view_mapping | 查詢 | authenticated | 專案擁有者或訂單相關用戶 |
| owner_manage_mapping | 全部 | authenticated | 專案擁有者 |

### 商品管理 (items)

| 政策名稱 | 操作 | 角色 | 條件 |
|---------|------|------|------|
| admin_manage_items | 全部 | authenticated | is_admin() |
| authenticated_view_items | 查詢 | authenticated | true |
| public_view_active_items | 查詢 | public | 僅活躍專案相關商品 |

### 訂單管理 (orders)

| 政策名稱 | 操作 | 角色 | 條件 |
|---------|------|------|------|
| admin_manage_orders | 全部 | authenticated | is_admin() |
| user_create_orders | 新增 | authenticated | 用戶ID匹配 |
| user_update_pending_orders | 更新 | authenticated | 用戶ID匹配 AND status = 'Pending' |
| user_view_own_orders | 查詢 | authenticated | 用戶ID匹配 |

### 訂單項目 (order_items)

| 政策名稱 | 操作 | 角色 | 條件 |
|---------|------|------|------|
| admin_manage_order_items | 全部 | authenticated | is_admin() |
| user_create_order_items | 新增 | authenticated | 訂單擁有者匹配 |
| user_update_pending_order_items | 更新 | authenticated | 訂單擁有者匹配 AND 訂單狀態 = 'Pending' |
| user_view_own_order_items | 查詢 | authenticated | 訂單擁有者匹配 |

注意：
1. 訂單相關政策的具體實現：
   ```sql
   -- 訂單政策
   user_id = auth.uid()::text  -- 基本用戶匹配
   AND status = 'Pending'      -- 狀態檢查（僅用於更新）

   -- 訂單項目政策
   EXISTS (
       SELECT 1 FROM orders
       WHERE orders.id = order_items.order_id
       AND orders.user_id = auth.uid()::text
       AND (
           CASE 
               WHEN TG_OP = 'UPDATE' THEN orders.status = 'Pending'
               ELSE TRUE
           END
       )
   )
   ```

2. 效能考量：
   - 使用 EXISTS 而不是 IN 或 JOIN 來提升效能
   - 對 orders.id 和 orders.user_id 建立適當的索引
   - 訂單狀態檢查使用精確匹配，便於使用索引
   - 使用 CASE 語句區分更新和查詢操作，避免不必要的狀態檢查

3. 安全性考量：
   - 所有操作都需要用戶認證（authenticated 角色）
   - 更新操作需要檢查訂單狀態
   - 訂單項目的權限繼承自訂單的擁有權
   - 避免使用子查詢返回不必要的欄位

4. 效能考量
   - is_admin() 函數使用 SECURITY DEFINER 提升效能
   - 監控複雜政策對查詢效能的影響
   - 優化涉及多表關聯的政策條件
   - 建議的索引設定：
     ```sql
     -- 專案相關
     CREATE INDEX IF NOT EXISTS idx_projects_status ON projects(project_status);
     CREATE INDEX IF NOT EXISTS idx_projects_owner ON projects(owner_id);
     
     -- 訂單相關
     CREATE INDEX IF NOT EXISTS idx_orders_user_status ON orders(user_id, status);
     CREATE INDEX IF NOT EXISTS idx_orders_project ON orders(project_id);
     
     -- 訂單項目相關
     CREATE INDEX IF NOT EXISTS idx_order_items_order ON order_items(order_id);
     
     -- 折扣映射相關
     CREATE INDEX IF NOT EXISTS idx_discount_mappings_project ON discount_project_mappings(project_id);
     CREATE INDEX IF NOT EXISTS idx_discount_mappings_discount ON discount_project_mappings(discount_id);
     ```
   - 複合索引考量：
     - 對於常用的組合條件（如 user_id + status）使用複合索引
     - 確保索引的欄位順序符合查詢模式
     - 避免創建重複或很少使用的索引

5. 公開存取限制
   - ✓ 已完成：限制公開存取僅限活躍專案相關資訊
   - ✓ 已完成：統一公開存取政策命名規範
   - ✓ 已完成：確保敏感資訊不會通過公開政策洩露

## 維護說明

1. 新增表格時的 RLS 檢查清單：
   - 確認是否需要啟用 RLS
   - 設計適當的存取政策
   - 如需管理員權限，使用 is_admin() 函數
   - 測試所有可能的存取場景
   - 確保使用正確的角色（authenticated vs public）
   - 如果允許公開存取，限制在活躍專案範圍內

2. 修改現有政策時：
   - 確保不破壞現有功能
   - 使用統一的 is_admin() 函數進行管理員權限檢查
   - 確保狀態相關的限制（如適用）
   - 更新本文檔
   - 進行完整的存取測試

3. 定期安全審查：
   - 檢查政策有效性
   - 移除過時的政策
   - 確保所有管理員權限檢查都使用 is_admin() 函數
   - 檢查是否有不必要的 public 角色使用
   - 確保狀態檢查的完整性
   - 確保公開存取政策僅限於必要的活躍專案資訊
   - 更新安全性建議

## 安全性建議

1. 系統表格安全性
   - ✓ 已完成：為 id_sequences 啟用 RLS 並使用 is_admin() 進行權限控制
   - ✓ 已完成：為 project_status_logs 添加適當的存取控制

2. 角色權限優化
   - ✓ 已完成：將管理員政策改為 authenticated
   - ✓ 已完成：移除重複政策
   - ✓ 已完成：限制匿名存取僅限活躍專案
   - ✓ 已完成：移除中文政策名稱

3. 政策命名規範
   - ✓ 已完成：管理員政策統一使用 admin_manage_* 格式
   - ✓ 已完成：用戶相關政策使用描述性英文名稱
   - ✓ 已完成：移除所有中文政策名稱

4. 效能考量
   - is_admin() 函數使用 SECURITY DEFINER 提升效能
   - 監控複雜政策對查詢效能的影響
   - 優化涉及多表關聯的政策條件
   - 建議的索引設定：
     ```sql
     -- 專案相關
     CREATE INDEX IF NOT EXISTS idx_projects_status ON projects(project_status);
     CREATE INDEX IF NOT EXISTS idx_projects_owner ON projects(owner_id);
     
     -- 訂單相關
     CREATE INDEX IF NOT EXISTS idx_orders_user_status ON orders(user_id, status);
     CREATE INDEX IF NOT EXISTS idx_orders_project ON orders(project_id);
     
     -- 訂單項目相關
     CREATE INDEX IF NOT EXISTS idx_order_items_order ON order_items(order_id);
     
     -- 折扣映射相關
     CREATE INDEX IF NOT EXISTS idx_discount_mappings_project ON discount_project_mappings(project_id);
     CREATE INDEX IF NOT EXISTS idx_discount_mappings_discount ON discount_project_mappings(discount_id);
     ```
   - 複合索引考量：
     - 對於常用的組合條件（如 user_id + status）使用複合索引
     - 確保索引的欄位順序符合查詢模式
     - 避免創建重複或很少使用的索引

5. 公開存取限制
   - ✓ 已完成：限制公開存取僅限活躍專案相關資訊
   - ✓ 已完成：統一公開存取政策命名規範
   - ✓ 已完成：確保敏感資訊不會通過公開政策洩露 

## 後續事項

1. RLS 策略實施
   - 依照文件設定更新所有表格的 RLS 政策
   - 確認 is_admin() 函數的正確設置和權限
   - 驗證每個政策的 SQL 實現是否符合文件描述
   - 特別注意訂單項目更新邏輯的正確性

2. 索引優化
   - 創建文件中建議的所有索引
   - 監控索引使用情況
   - 根據實際查詢模式調整複合索引
   - 定期檢查索引效能報告

3. 全面測試計劃
   管理員測試：
   - [ ] 驗證所有表格的完整存取權限
   - [ ] 測試 is_admin() 函數在各種情境
   - [ ] 確認管理功能的正常運作

   已認證用戶測試：
   - [ ] 活躍專案的查看權限
   - [ ] 自己專案的管理權限
   - [ ] 訂單狀態限制的操作
   - [ ] 折扣映射的存取權限

   匿名用戶測試：
   - [ ] 確認只能查看活躍專案資訊
   - [ ] 驗證敏感資訊不會洩露
   - [ ] 測試公開 API 端點

4. 效能監控
   - 設置查詢效能監控
   - 記錄並分析慢查詢
   - 評估 RLS 對系統效能的影響
   - 建立效能基準和警報機制

5. 文件維護
   - 確保所有 SQL 實現與文件描述一致
   - 更新測試結果和效能數據
   - 記錄任何政策調整和原因
   - 保持與實際實現的同步 