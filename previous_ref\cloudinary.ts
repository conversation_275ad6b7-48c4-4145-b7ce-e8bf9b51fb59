/**
 * 舊版 TypeScript Cloudinary 服務 - 已更新為 JavaScript 版本
 * 新版本位於：public/app/js/services/cloudinaryService.js
 *
 * 以下代碼已註解，避免誤導和報錯
 */

/*
// 舊版 TypeScript 代碼 - 已廢棄
import { Cloudinary } from '@cloudinary/url-gen';

const cloudinaryConfig = {
  cloudName: import.meta.env.VITE_CLOUDINARY_CLOUD_NAME,
  apiKey: import.meta.env.VITE_CLOUDINARY_API_KEY,
  apiSecret: import.meta.env.VITE_CLOUDINARY_API_SECRET,
  uploadPreset: import.meta.env.VITE_CLOUDINARY_UPLOAD_PRESET,
};

if (!cloudinaryConfig.cloudName || !cloudinaryConfig.uploadPreset) {
  throw new Error('Missing Cloudinary configuration');
}

export const cld = new Cloudinary({
  cloud: {
    cloudName: cloudinaryConfig.cloudName,
  },
  url: {
    secure: true,
  },
});

export const getCloudinaryUploadUrl = () => {
  return `https://api.cloudinary.com/v1_1/${cloudinaryConfig.cloudName}/image/upload`;
};
*/

// 新版 JavaScript 實現請參考：
// public/app/js/services/cloudinaryService.js
// 新版本使用原生 fetch API，不依賴 @cloudinary/url-gen
