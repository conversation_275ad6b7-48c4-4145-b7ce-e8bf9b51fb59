/**
 * 舊版 TypeScript Supabase 服務 - 已更新為 JavaScript 版本
 * 新版本位於：public/app/js/services/supabaseService.js
 *
 * 以下代碼已註解，避免誤導和報錯
 */

/*
// 舊版 TypeScript 代碼 - 已廢棄
import { createClient } from '@supabase/supabase-js';
export * from '../types/supabase';

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL as string;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY as string;

export const supabase = createClient(supabaseUrl, supabaseAnonKey);
*/

// 新版 JavaScript 實現請參考：
// public/app/js/services/supabaseService.js

/*
// 舊版函數 - 已廢棄，新版請參考 public/app/js/services/supabaseService.js

export async function fetchActiveProjects() {
  const { data: projects, error } = await supabase
    .from('projects')
    .select(
      `
      id,
      project_id_display,
      name,
      description,
      project_status,
      default_discount_id,
      created_at,
      updated_at,
      deadline,
      arrival_date,
      images,
      highlights,
      owner_id
    `
    )
    .eq('project_status', 'active')
    .order('created_at', { ascending: false });

  if (error) {
    console.error('Error fetching active projects:', error);
    throw error;
  }

  if (!projects) return [];

  const projectsWithMinPrice = await Promise.all(
    projects.map(async project => {
      const { data: items, error: itemsError } = await supabase
        .from('items')
        .select('price')
        .eq('project_id', project.id);

      if (itemsError) {
        console.error('Error fetching items for project', project.id, itemsError);
        return { ...project, minPrice: null };
      }

      const prices = (items || []).map(item => item.price);
      const minPrice = prices.length > 0 ? Math.min(...prices) : null;

      return { ...project, minPrice };
    })
  );

  return projectsWithMinPrice;
}
*/

/*
// 以下所有函數已廢棄，新版 JavaScript 實現請參考：
// public/app/js/services/supabaseService.js

export async function fetchActiveProjectWithItems() {
  const { data: projects, error: projectError } = await supabase
    .from('projects')
    .select(
      `
      id,
      project_id_display,
      name,
      description,
      project_status,
      default_discount_id,
      created_at,
      updated_at,
      deadline,
      arrival_date,
      images,
      highlights,
      owner_id
    `
    )
    .eq('project_status', 'active')
    .order('created_at', { ascending: false })
    .limit(1)
    .single();

  if (projectError) {
    console.error('Error fetching active project:', projectError);
    throw projectError;
  }

  const { data: items, error: itemsError } = await supabase
    .from('items')
    .select('*')
    .eq('project_id', projects.id);

  if (itemsError) {
    console.error('Error fetching items for active project:', itemsError);
    throw itemsError;
  }

  return { project: projects, items: items || [] };
}

export const fetchProjectDiscounts = async (projectId: string) => {
  const now = new Date().toISOString();

  const { data, error } = await supabase
    .from('discounts')
    .select(
      `
      *,
      discount_project_mappings!inner(project_id)
    `
    )
    .eq('discount_project_mappings.project_id', projectId)
    .eq('active', true)
    .or(`start_date.is.null,start_date.lte.${now}`)
    .or(`end_date.is.null,end_date.gte.${now}`);

  if (error) handleSupabaseError(error);

  return data ?? [];
};

export function handleSupabaseError(error: any) {
  console.error('Supabase error:', error);
}

export async function checkSupabaseConnection(): Promise<{ ok: boolean; error?: any }> {
  try {
    const { error } = await supabase.from('projects').select('id').limit(1);
    if (error) {
      return { ok: false, error };
    }
    return { ok: true };
  } catch (err) {
    return { ok: false, error: err };
  }
}

export async function generateDisplayIdByRPC(prefix: string): Promise<string> {
  const { data, error } = await supabase.rpc('generate_display_id', { prefix });
  if (error || !data) {
    console.error('Error generating display ID:', error);
    throw error;
  }
  return data;
}

export async function createOrderWithItems(params: {
  userId: string;
  projectId: string;
  remarks: string;
  pickupDate: string; // ISO 字串
  cartItems: any[]; // 購物車陣列
}): Promise<string> {
  const { userId, projectId, remarks, pickupDate, cartItems } = params;
  const { data, error } = await supabase.rpc('create_order_with_items', {
    p_user_id: userId,
    p_project_id: projectId,
    p_remarks: remarks,
    p_pickup_date: pickupDate,
    p_cart_items: cartItems,
  });
  if (error || !data) {
    console.error('Error creating order:', error);
    throw error;
  }
  return data; // 返回新訂單 UUID
}

export async function fetchUserOrders(
  userId: string,
  filters?: { orderNumber?: string; projectId?: string; status?: string }
) {
  let query = supabase
    .from('orders')
    .select(
      `
      id,
      user_id,
      order_number,
      status,
      total_amount,
      created_at,
      updated_at,
      items,
      discounts,
      pickup_date,
      remarks
    `
    )
    .eq('user_id', userId);

  if (filters?.orderNumber) {
    query = query.eq('order_number', filters.orderNumber);
  }

  if (filters?.projectId) {
    query = query.eq('project_id', filters.projectId);
  }

  if (filters?.status) {
    query = query.eq('status', filters.status);
  }

  const { data, error } = await query.order('created_at', { ascending: false });

  if (error) {
    console.error('Error fetching user orders:', error);
    throw error;
  }

  return data || [];
}
*/
