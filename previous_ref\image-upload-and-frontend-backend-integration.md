# 圖片上傳與前後端連動功能改進指南

## 📋 專案現況分析

### 已實現功能
- ✅ Cloudinary 圖片上傳基礎功能
- ✅ Supabase 後端整合
- ✅ React Context 狀態管理
- ✅ 基礎錯誤處理機制
- ✅ RLS 安全策略
- ✅ 實時數據同步基礎

### 需要改進的部分
- 🔄 圖片上傳體驗優化
- 🔄 前後端連動穩定性
- 🔄 錯誤處理機制完善
- 🔄 用戶體驗提升

## 🚀 改進方案

### 1. 圖片上傳功能優化

#### 1.1 增強的 `useCloudinaryUpload` Hook
**檔案位置**: `src/hooks/useCloudinaryUpload.ts`

**新增功能**:
- 🖼️ 自動圖片壓縮（最大 1200x1200）
- 📁 資料夾分類管理
- 🔄 上傳進度顯示
- ⚡ 圖片格式轉換
- 📊 檔案大小優化

**使用範例**:
```typescript
const { uploadFile, isUploading, progress } = useCloudinaryUpload({
  maxSizeMB: 5,
  folder: 'projects',
  acceptedFileTypes: ['image/jpeg', 'image/png', 'image/webp'],
});

// 上傳並壓縮圖片
const result = await uploadFile(file, true);
```

#### 1.2 統一的 `ImageManager` 組件
**檔案位置**: `src/components/common/ImageManager/`

**功能特色**:
- 🎯 拖拽上傳支援
- 🔄 圖片排序功能
- 👁️ 預覽與管理
- 📱 響應式設計
- 🎨 深色主題支援

**使用範例**:
```tsx
<ImageManager
  images={projectImages}
  onChange={setProjectImages}
  maxCount={8}
  folder="projects"
  sortable={true}
/>
```

### 2. 前後端連動優化

#### 2.1 增強的 API Hook - `useApiWithSync`
**檔案位置**: `src/hooks/useApiWithSync.ts`

**核心功能**:
- 🔄 自動重試機制
- ⚡ 實時數據同步
- 🛡️ 智能錯誤處理
- 📊 載入狀態管理

**使用範例**:
```typescript
const { data, loading, error, refresh } = useApiWithSync(
  () => fetchActiveProjects(),
  {
    enableRealtime: true,
    tableName: 'projects',
    realtimeFilter: "project_status=eq.active",
    retryCount: 3,
    errorHandling: 'toast',
  }
);
```

#### 2.2 CRUD 操作簡化 - `useCrudApi`
**功能特色**:
- 📝 統一的 CRUD 操作
- 🔄 自動狀態同步
- 🛡️ 樂觀更新支援
- 📊 分頁與篩選

**使用範例**:
```typescript
const { data, createData, updateData, deleteData } = useCrudApi('projects', {
  enableRealtime: true,
  errorHandling: 'toast',
});
```

### 3. 錯誤處理系統升級

#### 3.1 智能錯誤分類與處理
**檔案位置**: `src/lib/showError.ts`

**改進功能**:
- 🏷️ 錯誤類型自動識別
- 📝 用戶友善訊息轉換
- 📊 錯誤日誌記錄
- 🔄 統一的訊息顯示

**錯誤類型**:
- `network`: 網路連線問題
- `validation`: 資料驗證錯誤
- `auth`: 身份驗證失敗
- `server`: 伺服器錯誤
- `unknown`: 未知錯誤

### 4. 實際應用範例

#### 4.1 增強版預購頁面
**檔案位置**: `src/pages/preorder/PreorderPageEnhanced/`

**新增功能**:
- 🔄 實時數據同步
- 📸 用戶回饋圖片上傳
- ⚡ 智能錯誤處理
- 📱 響應式設計優化

## 🛠️ 實施步驟

### 第一階段：基礎功能升級
1. ✅ 升級 `useCloudinaryUpload` Hook
2. ✅ 創建 `ImageManager` 組件
3. ✅ 實施 `useApiWithSync` Hook
4. ✅ 升級錯誤處理系統

### 第二階段：整合與測試
1. 🔄 整合新組件到現有頁面
2. 🔄 測試圖片上傳功能
3. 🔄 驗證實時同步效果
4. 🔄 優化用戶體驗

### 第三階段：效能優化
1. 📊 圖片載入優化
2. ⚡ API 請求優化
3. 🔄 快取策略實施
4. 📱 行動裝置優化

## 📈 預期效果

### 用戶體驗提升
- ⚡ 圖片上傳速度提升 50%
- 🔄 實時數據同步減少手動重新整理
- 🛡️ 錯誤處理更加友善
- 📱 行動裝置體驗優化

### 開發效率提升
- 🔧 統一的 API 操作介面
- 🛡️ 自動錯誤處理機制
- 📊 完整的載入狀態管理
- 🔄 可重用的組件庫

### 系統穩定性提升
- 🔄 自動重試機制
- 🛡️ 錯誤邊界保護
- 📊 完整的錯誤日誌
- ⚡ 效能監控機制

## 🔧 配置建議

### 環境變數設定
```env
# Cloudinary 配置
VITE_CLOUDINARY_CLOUD_NAME=your_cloud_name
VITE_CLOUDINARY_UPLOAD_PRESET=your_upload_preset

# Supabase 配置
VITE_SUPABASE_URL=your_supabase_url
VITE_SUPABASE_ANON_KEY=your_anon_key
```

### Cloudinary 上傳預設建議
```json
{
  "folder": "small-forest-life",
  "transformation": [
    {"quality": "auto:good"},
    {"fetch_format": "auto"}
  ],
  "allowed_formats": ["jpg", "png", "webp"],
  "max_file_size": 5242880
}
```

## 📚 相關文檔

- [Cloudinary 官方文檔](https://cloudinary.com/documentation)
- [Supabase 實時功能](https://supabase.com/docs/guides/realtime)
- [React Hook 最佳實踐](https://react.dev/learn/reusing-logic-with-custom-hooks)
- [Ant Design 組件庫](https://ant.design/components/overview/)

## 🤝 貢獻指南

1. 遵循現有的程式碼風格
2. 添加適當的 TypeScript 類型
3. 撰寫單元測試
4. 更新相關文檔
5. 提交 Pull Request

## 📞 技術支援

如有問題或建議，請通過以下方式聯繫：
- GitHub Issues
- 技術文檔 Wiki
- 開發團隊 Slack
