/**
 * 用戶認證狀態管理
 * 使用 Nano Stores 管理用戶認證狀態
 */

import { atom, computed } from 'nanostores';

// 用戶資訊結構：
// {
//   id: string,           // LINE User ID
//   displayName: string,  // LINE 顯示名稱
//   pictureUrl: string,   // 頭像圖片網址
//   role: string,         // 用戶角色 (user, admin, manager)
//   communityNickname: string, // 社群暱稱
//   lastLoginAt: string   // 最後登入時間
// }

// 用戶資訊狀態
export const userInfo = atom(null);

// LIFF 初始化狀態
export const liffInitialized = atom(false);

// 登入狀態
export const isLoggedIn = computed(userInfo, (user) => {
  return user !== null;
});

// 管理員登入狀態
export const isAdminLoggedIn = computed(userInfo, (user) => {
  return user !== null && (user.role === 'admin' || user.role === 'manager');
});

// 用戶角色檢查
export const userRole = computed(userInfo, (user) => {
  return user?.role || 'user';
});

// 認證操作函數

/**
 * 設置用戶資訊
 * @param {Object} user - 用戶資訊
 */
export function setUserInfo(user) {
  userInfo.set(user);
}

/**
 * 清除用戶資訊（登出）
 */
export function clearUserInfo() {
  userInfo.set(null);
}

/**
 * 更新最後登入時間
 */
export function updateLastLoginTime() {
  const currentUser = userInfo.get();
  if (currentUser) {
    userInfo.set({
      ...currentUser,
      lastLoginAt: new Date().toISOString()
    });
  }
}

/**
 * 檢查用戶是否有指定角色
 * @param {string} role - 角色名稱
 * @returns {boolean} 是否有該角色
 */
export function hasRole(role) {
  const currentUser = userInfo.get();
  return currentUser?.role === role;
}

/**
 * 檢查用戶是否有管理員權限
 * @returns {boolean} 是否有管理員權限
 */
export function hasAdminPermission() {
  const currentUser = userInfo.get();
  return currentUser?.role === 'admin' || currentUser?.role === 'manager';
}

/**
 * 獲取用戶顯示名稱
 * @returns {string} 顯示名稱
 */
export function getDisplayName() {
  const currentUser = userInfo.get();
  return currentUser?.communityNickname || currentUser?.displayName || '用戶';
}

/**
 * 獲取用戶頭像
 * @returns {string} 頭像URL
 */
export function getUserAvatar() {
  const currentUser = userInfo.get();
  return currentUser?.pictureUrl || '';
}

/**
 * 設置 LIFF 初始化狀態
 * @param {boolean} initialized - 是否已初始化
 */
export function setLiffInitialized(initialized) {
  liffInitialized.set(initialized);
}
