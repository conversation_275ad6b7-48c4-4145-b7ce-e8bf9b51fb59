/**
 * 日期工具函數
 * 從 previous_ref/dateUtils.ts 改寫為 Vanilla JavaScript
 */

/**
 * 格式化日期時間
 * @param {string|Date} date - 日期時間字串或 Date 物件
 * @returns {string} 格式化後的日期時間字串
 */
export function formatDateTime(date) {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  
  if (!isValidDate(dateObj)) {
    return '無效日期';
  }

  return new Intl.DateTimeFormat('zh-TW', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false
  }).format(dateObj);
}

/**
 * 格式化日期
 * @param {string|Date} date - 日期字串或 Date 物件
 * @returns {string} 格式化後的日期字串
 */
export function formatDate(date) {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  
  if (!isValidDate(dateObj)) {
    return '無效日期';
  }

  return new Intl.DateTimeFormat('zh-TW', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  }).format(dateObj);
}

/**
 * 格式化時間
 * @param {string|Date} date - 日期時間字串或 Date 物件
 * @returns {string} 格式化後的時間字串
 */
export function formatTime(date) {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  
  if (!isValidDate(dateObj)) {
    return '無效時間';
  }

  return new Intl.DateTimeFormat('zh-TW', {
    hour: '2-digit',
    minute: '2-digit',
    hour12: false
  }).format(dateObj);
}

/**
 * 檢查日期是否有效
 * @param {string|Date} date - 日期字串或 Date 物件
 * @returns {boolean} 是否為有效日期
 */
export function isValidDate(date) {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return dateObj instanceof Date && !isNaN(dateObj.getTime());
}

/**
 * 獲取相對時間描述
 * @param {string|Date} date - 日期時間字串或 Date 物件
 * @returns {string} 相對時間描述
 */
export function getRelativeTime(date) {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  
  if (!isValidDate(dateObj)) {
    return '無效日期';
  }

  const now = new Date();
  const diffMs = now.getTime() - dateObj.getTime();
  const diffMinutes = Math.floor(diffMs / (1000 * 60));
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

  if (diffMinutes < 1) {
    return '剛剛';
  } else if (diffMinutes < 60) {
    return `${diffMinutes} 分鐘前`;
  } else if (diffHours < 24) {
    return `${diffHours} 小時前`;
  } else if (diffDays < 7) {
    return `${diffDays} 天前`;
  } else {
    return formatDate(dateObj);
  }
}

/**
 * 檢查日期是否為今天
 * @param {string|Date} date - 日期字串或 Date 物件
 * @returns {boolean} 是否為今天
 */
export function isToday(date) {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  
  if (!isValidDate(dateObj)) {
    return false;
  }

  const today = new Date();
  return dateObj.toDateString() === today.toDateString();
}

/**
 * 檢查日期是否為明天
 * @param {string|Date} date - 日期字串或 Date 物件
 * @returns {boolean} 是否為明天
 */
export function isTomorrow(date) {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  
  if (!isValidDate(dateObj)) {
    return false;
  }

  const tomorrow = new Date();
  tomorrow.setDate(tomorrow.getDate() + 1);
  return dateObj.toDateString() === tomorrow.toDateString();
}

/**
 * 檢查日期是否已過期
 * @param {string|Date} date - 日期字串或 Date 物件
 * @returns {boolean} 是否已過期
 */
export function isPast(date) {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  
  if (!isValidDate(dateObj)) {
    return false;
  }

  return dateObj.getTime() < new Date().getTime();
}

/**
 * 檢查日期是否在未來
 * @param {string|Date} date - 日期字串或 Date 物件
 * @returns {boolean} 是否在未來
 */
export function isFuture(date) {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  
  if (!isValidDate(dateObj)) {
    return false;
  }

  return dateObj.getTime() > new Date().getTime();
}

/**
 * 計算兩個日期之間的天數差
 * @param {string|Date} date1 - 第一個日期
 * @param {string|Date} date2 - 第二個日期
 * @returns {number} 天數差（正數表示 date1 在 date2 之後）
 */
export function getDaysDifference(date1, date2) {
  const dateObj1 = typeof date1 === 'string' ? new Date(date1) : date1;
  const dateObj2 = typeof date2 === 'string' ? new Date(date2) : date2;
  
  if (!isValidDate(dateObj1) || !isValidDate(dateObj2)) {
    return 0;
  }

  const diffMs = dateObj1.getTime() - dateObj2.getTime();
  return Math.floor(diffMs / (1000 * 60 * 60 * 24));
}

/**
 * 獲取日期的開始時間（00:00:00）
 * @param {string|Date} date - 日期字串或 Date 物件
 * @returns {Date} 日期的開始時間
 */
export function getStartOfDay(date) {
  const dateObj = typeof date === 'string' ? new Date(date) : new Date(date);
  dateObj.setHours(0, 0, 0, 0);
  return dateObj;
}

/**
 * 獲取日期的結束時間（23:59:59）
 * @param {string|Date} date - 日期字串或 Date 物件
 * @returns {Date} 日期的結束時間
 */
export function getEndOfDay(date) {
  const dateObj = typeof date === 'string' ? new Date(date) : new Date(date);
  dateObj.setHours(23, 59, 59, 999);
  return dateObj;
}

/**
 * 格式化為 ISO 日期字串（YYYY-MM-DD）
 * @param {string|Date} date - 日期字串或 Date 物件
 * @returns {string} ISO 日期字串
 */
export function toISODateString(date) {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  
  if (!isValidDate(dateObj)) {
    return '';
  }

  return dateObj.toISOString().split('T')[0];
}

/**
 * 從 ISO 日期字串創建 Date 物件
 * @param {string} isoString - ISO 日期字串
 * @returns {Date|null} Date 物件或 null
 */
export function fromISODateString(isoString) {
  if (!isoString) {
    return null;
  }

  const dateObj = new Date(isoString);
  return isValidDate(dateObj) ? dateObj : null;
}
