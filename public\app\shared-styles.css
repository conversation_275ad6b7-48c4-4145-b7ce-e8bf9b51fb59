/* --- 设计系统基础变量 --- */
:root {
  /* 颜色系统 */
  --primary-color: #57AC5A;
  --primary-hover: #aedfc0;
  --primary-light: rgba(87, 172, 90, 0.1);
  --primary-lighter: rgba(87, 172, 90, 0.05);
  --secondary-color: #FFC75A;
  --secondary-dark: #704800; /* Added for cart badge text */
  --danger-color: #e74c3c;
  --danger-light: rgba(231, 76, 60, 0.1);
  --warning-color: #f1c40f;
  --warning-light: rgba(241, 196, 15, 0.1);
  --info-color: #3498db;
  --info-light: rgba(52, 152, 219, 0.1);
  --text-dark: #333333;
  --text-medium: #666666;
  --text-light: #888888;
  --border-color: #e0e0e0;
  --border-light: #f0f0f0; /* Lighter border for internal separation */
  --bg-light: #f7f9fc;
  --bg-white: #ffffff;
  --bg-medium: #f9f9f9; /* Slightly off-white bg */

  /* 新增：導航欄背景色 */
  --header-bg: #3C8A3F;  /* 更深的綠色 */
  --header-text: #ffffff; /* 白色文字 */
  --header-text-light: rgba(255, 255, 255, 0.85); /* 次要白色文字 */
  --bottom-nav-bg: #FFFDF6; /* 溫暖奶油色 */

  /* 间距系统 */
  --space-xs: 4px;
  --space-sm: 8px;
  --space-md: 16px;
  --space-lg: 24px;
  --space-xl: 32px;

  /* 圆角系统 */
  --radius-sm: 4px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-full: 9999px;

  /* 阴影系统 */
  --shadow-sm: 0 1px 3px rgba(0,0,0,0.08); /* Slightly adjusted */
  --shadow-md: 0 3px 10px rgba(0,0,0,0.06); /* Slightly adjusted */
  --shadow-lg: 0 5px 15px rgba(0,0,0,0.08); /* Slightly adjusted */

  /* 字体 */
  --font-family-base: 'Noto Sans TC', 'Roboto', sans-serif;
  --font-size-sm: 0.8rem;
  --font-size-base: 1rem;
  --font-size-md: 1.125rem;
  --font-size-lg: 1.25rem;
  --font-size-xl: 1.5rem;
  --font-size-xxl: 1.8rem; /* Updated */
  --font-size-xxxl: 2rem;  /* Updated */

  --line-height-base: 1.6;
  --line-height-tight: 1.4;

  /* Layout Variables */
  --app-header-height: 60px; /* Approximate height */
  --bottom-nav-height: 65px; /* Approximate height */
}

/* --- 基础重置与全局样式 --- */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  font-size: 16px; /* Base font size */
}

body {
  font-family: var(--font-family-base);
  line-height: var(--line-height-base);
  color: var(--text-dark);
  background-color: var(--bg-light);
  min-height: 100vh;
  /* Add padding bottom to prevent content from hiding behind bottom nav */
  padding-bottom: var(--bottom-nav-height);
}

a {
  color: var(--primary-color);
  text-decoration: none;
  transition: color 0.2s ease;
}

a:hover {
  color: var(--primary-hover);
}

img {
  max-width: 100%;
  height: auto;
  display: block; /* Prevents bottom space */
}

/* --- 通用布局容器 --- */
.main-content {
  padding-top: var(--app-header-height);
  /* padding-bottom is handled by body padding */
  min-height: calc(100vh - var(--app-header-height)); /* Ensure content fills height */
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding-left: var(--space-md);
  padding-right: var(--space-md);
}

/* --- 共享组件 --- */

/* 按钮 Button */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-sm);
  padding: var(--space-sm) var(--space-md);
  border-radius: var(--radius-md);
  font-weight: 500;
  font-size: var(--font-size-base);
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: center; /* Ensure text centers if button becomes block */
  white-space: nowrap; /* Prevent text wrapping */
}

.btn-primary {
  background-color: var(--primary-color);
  color: var(--bg-white);
  box-shadow: var(--shadow-sm);
}

.btn-primary:hover {
  background-color: var(--primary-hover);
  box-shadow: var(--shadow-md);
  transform: translateY(-1px); /* Subtle lift */
}

.btn-primary:active {
    transform: translateY(0);
    box-shadow: var(--shadow-sm);
}

.btn-outline {
  background-color: transparent;
  color: var(--primary-color);
  border: 2px solid var(--primary-color);
}

.btn-outline:hover {
  background-color: var(--primary-light);
}

.btn-sm {
  padding: var(--space-xs) var(--space-sm);
  font-size: var(--font-size-sm);
  border-radius: var(--radius-sm);
}

.btn-lg {
    padding: var(--space-md) var(--space-lg);
    font-size: var(--font-size-md);
    border-radius: var(--radius-lg);
}

/* 卡片 Card */
.card {
  background-color: var(--bg-white);
  border-radius: var(--radius-lg); /* Use larger radius for consistency */
  box-shadow: var(--shadow-md);
  overflow: hidden;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

/* 表单元素 Form Elements */
.form-input,
.form-select,
.form-textarea {
  width: 100%;
  padding: var(--space-sm) var(--space-md); /* Consistent padding */
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  font-size: var(--font-size-base);
  line-height: var(--line-height-base);
  background-color: var(--bg-white);
  transition: all 0.2s ease;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px var(--primary-light);
}

.form-textarea {
    min-height: 100px;
    resize: vertical;
}

.form-label {
    display: block;
    font-weight: 500;
    margin-bottom: var(--space-sm);
    color: var(--text-medium);
    font-size: var(--font-size-sm);
}

.form-group {
    margin-bottom: var(--space-lg);
}

/* 状态标签 Status Tag */
.status {
  display: inline-flex;
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-full);
  font-size: var(--font-size-sm);
  font-weight: 600;
  line-height: var(--line-height-tight);
  text-transform: uppercase; /* Optional: make status text uppercase */
}

.status-active, /* For generic active state */
.status-ongoing { /* More specific */
  background-color: var(--primary-light);
  color: var(--primary-color);
}

.status-pending {
  background-color: var(--warning-light);
  color: var(--warning-color);
}

.status-completed {
  background-color: var(--primary-light); /* Use primary for completed as well */
  color: var(--primary-color);
}

.status-confirmed {
  background-color: var(--info-light);
  color: var(--info-color);
}

.status-cancelled,
.status-inactive { /* Group similar visual styles */
  background-color: var(--danger-light);
  color: var(--danger-color);
}

/* 用户控制/头像 User Control / Avatar */
.user-control {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    cursor: pointer; /* Make the whole control clickable if it acts as a dropdown trigger */
}

.user-avatar {
    width: 36px; /* Slightly larger standard size */
    height: 36px;
    border-radius: 50%;
    overflow: hidden;
    background-color: var(--primary-light); /* Fallback background */
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-color);
    font-weight: bold;
    flex-shrink: 0; /* Prevent shrinking in flex layouts */
    transition: transform 0.2s ease;
}

.user-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.user-avatar:hover {
    transform: scale(1.05);
}

.user-name {
    font-weight: 500;
    font-size: var(--font-size-base);
    color: var(--text-dark);
}

/* --- 页眉 App Header (Unified Navbar) --- */
.app-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: var(--app-header-height);
  padding: 0 var(--space-md);
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: var(--header-bg);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  z-index: 1000;
}

.app-header .logo {
  display: flex;
  align-items: center;
  text-decoration: none;
}

.app-header .logo-img {
  width: 42px;
  height: 42px;
  margin-right: var(--space-sm);
  object-fit: contain;
  filter: none;
}

.app-header .logo-text {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--header-text);
}

.app-header .nav-right {
  display: flex;
  align-items: center;
  gap: var(--space-md);
}

.app-header .cart-btn {
  position: relative;
  background: none;
  border: none;
  color: var(--header-text-light);
  font-size: var(--font-size-lg);
  padding: var(--space-sm);
  cursor: pointer;
  line-height: 1;
}

.app-header .cart-btn:hover {
  color: var(--header-text);
}

.app-header .cart-badge {
  position: absolute;
  top: -4px; /* 調整位置 */
  right: -4px; /* 調整位置 */
  min-width: 18px;
  height: 18px;
  padding: 0 4px;
  background-color: var(--secondary-color);
  color: var(--secondary-dark);
  border-radius: var(--radius-full);
  font-size: 0.75rem;
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: bold;
  line-height: 1;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1); /* 添加陰影替代邊框 */
  border: none; /* 移除白色邊框 */
}

/* --- 底部导航 Bottom Navigation --- */
.bottom-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: var(--bottom-nav-bg);
  display: flex;
  justify-content: space-around;
  padding: 8px 0 calc(8px + env(safe-area-inset-bottom));
  box-shadow: 0 -1px 8px rgba(0, 0, 0, 0.04);
  border-top: 1px solid rgba(0, 0, 0, 0.08);
  z-index: 100;
}

.nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-decoration: none;
  color: var(--text-medium);
  position: relative;
  transition: all 0.2s ease;
  padding: 6px 24px;
  min-width: 64px;
}

.nav-item::after {
  content: '';
  position: absolute;
  bottom: -4px;
  left: 50%;
  transform: translateX(-50%) scale(0);
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: var(--primary-color);
  transition: transform 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.nav-item .nav-icon {
  margin-bottom: 4px;
  opacity: 0.7;
  transition: all 0.2s ease;
}

.nav-label {
  font-size: 12px;
  font-weight: 500;
  transition: color 0.2s ease;
}

.nav-item:hover {
  color: var(--primary-color);
}

.nav-item:hover .nav-icon {
  opacity: 0.9;
  transform: translateY(-2px);
}

.nav-item.active {
  color: var(--primary-color);
}

.nav-item.active::after {
  transform: translateX(-50%) scale(1);
}

.nav-item.active .nav-icon {
  opacity: 1;
  transform: translateY(-2px);
}

/* 適配不同機型的底部安全區域 */
@supports (padding: max(0px)) {
  .bottom-nav {
    padding-bottom: max(8px, env(safe-area-inset-bottom));
  }
}

/* 動畫效果 */
@keyframes navItemActive {
  0% { transform: translateY(4px); opacity: 0.5; }
  100% { transform: translateY(0); opacity: 1; }
}

.nav-item.active .nav-icon {
  animation: navItemActive 0.2s ease forwards;
}

/* --- Utility Classes --- */
.sr-only { /* For accessibility */
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.text-center { text-align: center; }
.text-right { text-align: right; }

.mb-sm { margin-bottom: var(--space-sm); }
.mb-md { margin-bottom: var(--space-md); }
.mb-lg { margin-bottom: var(--space-lg); }
.mb-xl { margin-bottom: var(--space-xl); }

.mt-sm { margin-top: var(--space-sm); }
.mt-md { margin-top: var(--space-md); }
.mt-lg { margin-top: var(--space-lg); }
.mt-xl { margin-top: var(--space-xl); }