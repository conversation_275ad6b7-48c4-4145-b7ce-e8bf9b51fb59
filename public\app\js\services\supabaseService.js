/**
 * Supabase 服務層
 * 封裝與 Supabase 的所有互動
 * 使用 CDN 方式載入 Supabase
 */

// Supabase 配置
const supabaseUrl = 'https://wmemafmnulbtqvnkixwf.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6IndtZW1hZm1udWxidHF2bmtpeHdmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM2MDIxMTUsImV4cCI6MjA1OTE3ODExNX0.U7hIcTgm2iyIru9XxRI7lBUAYC-ogW3NRLmR4n2wLTA';

// Supabase 客戶端實例
let supabase = null;

/**
 * 初始化 Supabase 客戶端
 * @returns {boolean} 是否初始化成功
 */
function initializeSupabase() {
  if (typeof window !== 'undefined' && window.supabase) {
    supabase = window.supabase.createClient(supabaseUrl, supabaseAnonKey);
    console.log('Supabase client initialized');
    return true;
  }
  console.error('Supabase CDN not loaded');
  return false;
}

/**
 * 確保 Supabase 已初始化
 * @returns {Object} Supabase 客戶端
 */
function ensureSupabaseInitialized() {
  if (!supabase) {
    if (!initializeSupabase()) {
      throw new Error('Supabase client not available. Please ensure Supabase CDN is loaded.');
    }
  }
  return supabase;
}

/**
 * 錯誤處理函數
 * @param {Object} error - Supabase 錯誤對象
 */
export function handleSupabaseError(error) {
  console.error('Supabase error:', error);
  throw new Error(error.message || 'Database operation failed');
}

/**
 * 檢查 Supabase 連接
 * @returns {Promise<{ok: boolean, error?: any}>}
 */
export async function checkSupabaseConnection() {
  try {
    const client = ensureSupabaseInitialized();
    const { error } = await client.from('projects').select('id').limit(1);
    if (error) {
      return { ok: false, error };
    }
    return { ok: true };
  } catch (err) {
    return { ok: false, error: err };
  }
}

/**
 * 獲取活躍的專案列表
 * @returns {Promise<Array>} 專案列表
 */
export async function fetchActiveProjects() {
  try {
    const client = ensureSupabaseInitialized();
    const { data: projects, error } = await client
      .from('projects')
      .select(`
        id,
        project_id_display,
        name,
        description,
        project_status,
        default_discount_id,
        created_at,
        updated_at,
        deadline,
        arrival_date,
        images,
        highlights,
        owner_id
      `)
      .eq('project_status', 'active')
      .order('created_at', { ascending: false });

    if (error) {
      handleSupabaseError(error);
    }

    if (!projects) return [];

    // 為每個專案獲取最低價格
    const projectsWithMinPrice = await Promise.all(
      projects.map(async project => {
        const { data: items, error: itemsError } = await client
          .from('items')
          .select('price')
          .eq('project_id', project.id)
          .eq('status', 'Available');

        if (itemsError) {
          console.error('Error fetching items for project', project.id, itemsError);
          return { ...project, minPrice: null };
        }

        const prices = (items || []).map(item => item.price);
        const minPrice = prices.length > 0 ? Math.min(...prices) : null;

        return { ...project, minPrice };
      })
    );

    return projectsWithMinPrice;
  } catch (error) {
    console.error('Error fetching active projects:', error);
    throw error;
  }
}

/**
 * 獲取所有訂單列表（管理員功能）
 * @param {Object} filters - 篩選條件
 * @param {Object} pagination - 分頁參數
 * @returns {Promise<{orders: Array, totalCount: number}>} 訂單列表和總數
 */
export async function fetchAllOrders(filters = {}, pagination = { page: 1, limit: 20 }) {
  try {
    const client = ensureSupabaseInitialized();
    let query = client
      .from('orders')
      .select(`
        id,
        order_number,
        user_id,
        user_name,
        project_id,
        project_name,
        total_amount,
        discount_amount,
        manual_discount_amount,
        final_amount,
        status,
        order_date,
        pickup_date,
        notes,
        admin_notes,
        created_at,
        updated_at
      `, { count: 'exact' });

    // 應用篩選條件
    if (filters.orderNumber) {
      query = query.ilike('order_number', `%${filters.orderNumber}%`);
    }

    if (filters.userName) {
      query = query.ilike('user_name', `%${filters.userName}%`);
    }

    if (filters.projectId) {
      query = query.eq('project_id', filters.projectId);
    }

    if (filters.status) {
      query = query.eq('status', filters.status);
    }

    if (filters.startDate) {
      query = query.gte('order_date', filters.startDate);
    }

    if (filters.endDate) {
      query = query.lte('order_date', filters.endDate);
    }

    // 應用分頁
    const offset = (pagination.page - 1) * pagination.limit;
    query = query
      .order('created_at', { ascending: false })
      .range(offset, offset + pagination.limit - 1);

    const { data: orders, error, count } = await query;

    if (error) {
      handleSupabaseError(error);
    }

    return {
      orders: orders || [],
      totalCount: count || 0
    };
  } catch (error) {
    console.error('Error fetching all orders:', error);
    throw error;
  }
}

/**
 * 獲取訂單詳細資訊（包含訂單項目）
 * @param {string} orderId - 訂單ID
 * @returns {Promise<Object>} 訂單詳細資訊
 */
export async function fetchOrderDetails(orderId) {
  try {
    const client = ensureSupabaseInitialized();

    // 獲取訂單基本資訊
    const { data: order, error: orderError } = await client
      .from('orders')
      .select(`
        id,
        order_number,
        user_id,
        user_name,
        project_id,
        project_name,
        total_amount,
        discount_amount,
        manual_discount_amount,
        final_amount,
        status,
        order_date,
        pickup_date,
        notes,
        admin_notes,
        applied_discount_id,
        manual_discount_applied_by,
        manual_discount_applied_at,
        created_at,
        updated_at
      `)
      .eq('id', orderId)
      .single();

    if (orderError) {
      handleSupabaseError(orderError);
    }

    // 獲取訂單項目
    const { data: orderItems, error: itemsError } = await client
      .from('order_items')
      .select(`
        id,
        item_id,
        item_name,
        quantity,
        unit_price,
        subtotal,
        created_at
      `)
      .eq('order_id', orderId)
      .order('created_at', { ascending: true });

    if (itemsError) {
      handleSupabaseError(itemsError);
    }

    return {
      ...order,
      items: orderItems || []
    };
  } catch (error) {
    console.error('Error fetching order details:', error);
    throw error;
  }
}

/**
 * 獲取所有專案列表（用於篩選下拉選單）
 * @returns {Promise<Array>} 專案列表
 */
export async function fetchAllProjects() {
  try {
    const client = ensureSupabaseInitialized();
    const { data, error } = await client
      .from('projects')
      .select(`
        id,
        project_id_display,
        name,
        project_status
      `)
      .order('created_at', { ascending: false });

    if (error) {
      handleSupabaseError(error);
    }

    return data || [];
  } catch (error) {
    console.error('Error fetching all projects:', error);
    throw error;
  }
}

/**
 * 更新訂單狀態
 * @param {string} orderId - 訂單ID
 * @param {string} newStatus - 新狀態
 * @param {string} adminNotes - 管理員備註
 * @returns {Promise<Object>} 更新後的訂單資訊
 */
export async function updateOrderStatus(orderId, newStatus, adminNotes = '') {
  try {
    const client = ensureSupabaseInitialized();
    const { data, error } = await client
      .from('orders')
      .update({
        status: newStatus,
        admin_notes: adminNotes,
        updated_at: new Date().toISOString()
      })
      .eq('id', orderId)
      .select()
      .single();

    if (error) {
      handleSupabaseError(error);
    }

    return data;
  } catch (error) {
    console.error('Error updating order status:', error);
    throw error;
  }
}

/**
 * 批量更新訂單狀態
 * @param {Array<string>} orderIds - 訂單ID列表
 * @param {string} newStatus - 新狀態
 * @param {string} adminNotes - 管理員備註
 * @returns {Promise<Array>} 更新後的訂單列表
 */
export async function batchUpdateOrderStatus(orderIds, newStatus, adminNotes = '') {
  try {
    const client = ensureSupabaseInitialized();
    const { data, error } = await client
      .from('orders')
      .update({
        status: newStatus,
        admin_notes: adminNotes,
        updated_at: new Date().toISOString()
      })
      .in('id', orderIds)
      .select();

    if (error) {
      handleSupabaseError(error);
    }

    return data || [];
  } catch (error) {
    console.error('Error batch updating order status:', error);
    throw error;
  }
}

/**
 * 刪除訂單（軟刪除或硬刪除）
 * @param {Array<string>} orderIds - 訂單ID列表
 * @returns {Promise<boolean>} 刪除是否成功
 */
export async function deleteOrders(orderIds) {
  try {
    const client = ensureSupabaseInitialized();
    // 這裡實現軟刪除，將狀態設為 'Cancelled'
    const { error } = await client
      .from('orders')
      .update({
        status: 'Cancelled',
        admin_notes: '管理員刪除',
        updated_at: new Date().toISOString()
      })
      .in('id', orderIds);

    if (error) {
      handleSupabaseError(error);
    }

    return true;
  } catch (error) {
    console.error('Error deleting orders:', error);
    throw error;
  }
}

// 導出初始化函數
export { initializeSupabase, ensureSupabaseInitialized };
