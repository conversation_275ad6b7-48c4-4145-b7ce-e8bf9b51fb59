/**
 * Supabase 服務層
 * 封裝與 Supabase 的所有互動
 * 使用 CDN 方式載入 Supabase
 */

// Supabase 配置
const supabaseUrl = 'https://wmemafmnulbtqvnkixwf.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6IndtZW1hZm1udWxidHF2bmtpeHdmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM2MDIxMTUsImV4cCI6MjA1OTE3ODExNX0.U7hIcTgm2iyIru9XxRI7lBUAYC-ogW3NRLmR4n2wLTA';

// Supabase 客戶端實例
let supabase = null;

/**
 * 初始化 Supabase 客戶端
 * @returns {boolean} 是否初始化成功
 */
function initializeSupabase() {
  if (typeof window !== 'undefined' && window.supabase) {
    supabase = window.supabase.createClient(supabaseUrl, supabaseAnonKey);
    console.log('Supabase client initialized');
    return true;
  }
  console.error('Supabase CDN not loaded');
  return false;
}

/**
 * 確保 Supabase 已初始化
 * @returns {Object} Supabase 客戶端
 */
function ensureSupabaseInitialized() {
  if (!supabase) {
    if (!initializeSupabase()) {
      throw new Error('Supabase client not available. Please ensure Supabase CDN is loaded.');
    }
  }
  return supabase;
}

/**
 * 錯誤處理函數
 * @param {Object} error - Supabase 錯誤對象
 */
export function handleSupabaseError(error) {
  console.error('Supabase error:', error);
  throw new Error(error.message || 'Database operation failed');
}

/**
 * 檢查 Supabase 連接
 * @returns {Promise<{ok: boolean, error?: any}>}
 */
export async function checkSupabaseConnection() {
  try {
    const client = ensureSupabaseInitialized();
    const { error } = await client.from('projects').select('id').limit(1);
    if (error) {
      return { ok: false, error };
    }
    return { ok: true };
  } catch (err) {
    return { ok: false, error: err };
  }
}

/**
 * 獲取活躍的專案列表
 * @returns {Promise<Array>} 專案列表
 */
export async function fetchActiveProjects() {
  try {
    const client = ensureSupabaseInitialized();

    // 使用 RPC 函數獲取活躍專案，繞過 RLS 限制
    const { data: projects, error } = await client.rpc('get_active_projects');

    if (error) {
      handleSupabaseError(error);
    }

    if (!projects) return [];

    // 轉換資料格式，確保與前端期望的格式一致
    return projects.map(project => ({
      ...project,
      minPrice: project.min_price ? parseFloat(project.min_price) : null
    }));
  } catch (error) {
    console.error('Error fetching active projects:', error);
    throw error;
  }
}

/**
 * 獲取所有訂單列表（管理員功能）
 * @param {Object} filters - 篩選條件
 * @param {Object} pagination - 分頁參數
 * @returns {Promise<{orders: Array, totalCount: number}>} 訂單列表和總數
 */
export async function fetchAllOrders(filters = {}, pagination = { page: 1, limit: 20 }) {
  try {
    const client = ensureSupabaseInitialized();

    // 使用 RPC 函數繞過 RLS
    const offset = (pagination.page - 1) * pagination.limit;

    const { data, error } = await client.rpc('admin_get_all_orders', {
      p_limit: pagination.limit,
      p_offset: offset,
      p_order_number: filters.orderNumber || null,
      p_user_name: filters.userName || null,
      p_project_id: filters.projectId || null,
      p_status: filters.status || null,
      p_start_date: filters.startDate || null,
      p_end_date: filters.endDate || null
    });

    if (error) {
      handleSupabaseError(error);
    }

    const orders = data || [];
    const totalCount = orders.length > 0 ? orders[0].total_count : 0;

    // 移除 total_count 欄位
    const cleanOrders = orders.map(order => {
      const { total_count, ...cleanOrder } = order;
      return cleanOrder;
    });

    return {
      orders: cleanOrders,
      totalCount: parseInt(totalCount) || 0
    };
  } catch (error) {
    console.error('Error fetching all orders:', error);
    throw error;
  }
}

/**
 * 獲取訂單詳細資訊（包含訂單項目）
 * @param {string} orderId - 訂單ID
 * @returns {Promise<Object>} 訂單詳細資訊
 */
export async function fetchOrderDetails(orderId) {
  try {
    const client = ensureSupabaseInitialized();

    // 使用 RPC 函數獲取訂單詳情
    const { data, error } = await client.rpc('admin_get_order_details', {
      p_order_id: orderId
    });

    if (error) {
      handleSupabaseError(error);
    }

    if (!data || !data.order) {
      throw new Error('Order not found');
    }

    return {
      ...data.order,
      items: data.items || []
    };
  } catch (error) {
    console.error('Error fetching order details:', error);
    throw error;
  }
}

/**
 * 獲取指定專案及其商品
 * @param {string} projectId - 專案ID
 * @returns {Promise<Object>} 專案和商品資料
 */
export async function fetchProjectWithItems(projectId) {
  try {
    const client = ensureSupabaseInitialized();

    // 使用 RPC 函數獲取專案詳情
    const { data: projectData, error: projectError } = await client.rpc('get_project_with_items', {
      p_project_id: projectId
    });

    if (projectError) {
      handleSupabaseError(projectError);
    }

    if (!projectData || !projectData.project) {
      throw new Error('Project not found');
    }

    return {
      project: projectData.project,
      items: projectData.items || []
    };
  } catch (error) {
    console.error('Error fetching project with items:', error);
    throw error;
  }
}

/**
 * 獲取專案的折扣規則
 * @param {string} projectId - 專案ID
 * @returns {Promise<Array>} 折扣規則列表
 */
export async function fetchProjectDiscounts(projectId) {
  try {
    const client = ensureSupabaseInitialized();

    // 使用 RPC 函數獲取專案折扣
    const { data, error } = await client.rpc('get_project_discounts', {
      p_project_id: projectId
    });

    if (error) {
      handleSupabaseError(error);
    }

    return data || [];
  } catch (error) {
    console.error('Error fetching project discounts:', error);
    throw error;
  }
}

/**
 * 創建訂單及其項目
 * @param {Object} orderData - 訂單資料
 * @returns {Promise<string>} 新訂單ID
 */
export async function createOrderWithItems(orderData) {
  try {
    const client = ensureSupabaseInitialized();

    // 使用 RPC 函數創建訂單
    const { data: orderId, error } = await client.rpc('create_order_with_items', {
      p_user_id: orderData.userId,
      p_project_id: orderData.projectId,
      p_remarks: orderData.remarks || null,
      p_pickup_date: orderData.pickupDate || null,
      p_cart_items: JSON.stringify(orderData.cartItems)
    });

    if (error) {
      handleSupabaseError(error);
    }

    return orderId;
  } catch (error) {
    console.error('Error creating order with items:', error);
    throw error;
  }
}

/**
 * 獲取用戶的訂單列表
 * @param {string} userId - 用戶ID
 * @returns {Promise<Array>} 用戶訂單列表
 */
export async function fetchUserOrders(userId) {
  try {
    const client = ensureSupabaseInitialized();

    // 使用 RPC 函數獲取用戶訂單
    const { data, error } = await client.rpc('get_user_orders', {
      p_user_id: userId
    });

    if (error) {
      handleSupabaseError(error);
    }

    return data || [];
  } catch (error) {
    console.error('Error fetching user orders:', error);
    throw error;
  }
}

/**
 * 獲取所有專案列表（用於篩選下拉選單）
 * @returns {Promise<Array>} 專案列表
 */
export async function fetchAllProjects() {
  try {
    const client = ensureSupabaseInitialized();
    const { data, error } = await client
      .from('projects')
      .select(`
        id,
        project_id_display,
        name,
        project_status
      `)
      .order('created_at', { ascending: false });

    if (error) {
      handleSupabaseError(error);
    }

    return data || [];
  } catch (error) {
    console.error('Error fetching all projects:', error);
    throw error;
  }
}

/**
 * 更新訂單狀態
 * @param {string} orderId - 訂單ID
 * @param {string} newStatus - 新狀態
 * @param {string} adminNotes - 管理員備註
 * @returns {Promise<Object>} 更新後的訂單資訊
 */
export async function updateOrderStatus(orderId, newStatus, adminNotes = '') {
  try {
    const client = ensureSupabaseInitialized();
    const { data, error } = await client
      .from('orders')
      .update({
        status: newStatus,
        admin_notes: adminNotes,
        updated_at: new Date().toISOString()
      })
      .eq('id', orderId)
      .select()
      .single();

    if (error) {
      handleSupabaseError(error);
    }

    return data;
  } catch (error) {
    console.error('Error updating order status:', error);
    throw error;
  }
}

/**
 * 批量更新訂單狀態
 * @param {Array<string>} orderIds - 訂單ID列表
 * @param {string} newStatus - 新狀態
 * @param {string} adminNotes - 管理員備註
 * @returns {Promise<Array>} 更新後的訂單列表
 */
export async function batchUpdateOrderStatus(orderIds, newStatus, adminNotes = '') {
  try {
    const client = ensureSupabaseInitialized();
    const { data, error } = await client
      .from('orders')
      .update({
        status: newStatus,
        admin_notes: adminNotes,
        updated_at: new Date().toISOString()
      })
      .in('id', orderIds)
      .select();

    if (error) {
      handleSupabaseError(error);
    }

    return data || [];
  } catch (error) {
    console.error('Error batch updating order status:', error);
    throw error;
  }
}

/**
 * 刪除訂單（軟刪除或硬刪除）
 * @param {Array<string>} orderIds - 訂單ID列表
 * @returns {Promise<boolean>} 刪除是否成功
 */
export async function deleteOrders(orderIds) {
  try {
    const client = ensureSupabaseInitialized();
    // 這裡實現軟刪除，將狀態設為 'Cancelled'
    const { error } = await client
      .from('orders')
      .update({
        status: 'Cancelled',
        admin_notes: '管理員刪除',
        updated_at: new Date().toISOString()
      })
      .in('id', orderIds);

    if (error) {
      handleSupabaseError(error);
    }

    return true;
  } catch (error) {
    console.error('Error deleting orders:', error);
    throw error;
  }
}

/**
 * 管理員身份驗證（開發用）
 * @param {string} adminUserId - 管理員用戶ID
 * @returns {Promise<boolean>} 是否驗證成功
 */
export async function authenticateAsAdmin(adminUserId = 'admin_test_user') {
  try {
    const client = ensureSupabaseInitialized();

    // 使用 RPC 函數進行身份驗證
    const { data, error } = await client.rpc('admin_authenticate', {
      p_admin_id: adminUserId
    });

    if (error) {
      console.error('Admin authentication failed:', error);
      return false;
    }

    if (!data || !data.success) {
      console.error('Admin authentication failed:', data?.error || 'Unknown error');
      return false;
    }

    // 儲存管理員身份到 localStorage
    localStorage.setItem('admin_user', JSON.stringify(data.user));

    console.log('Admin authenticated successfully:', data.user);
    return true;
  } catch (error) {
    console.error('Error authenticating admin:', error);
    return false;
  }
}

/**
 * 檢查當前是否為管理員
 * @returns {boolean} 是否為管理員
 */
export function isCurrentUserAdmin() {
  try {
    const adminUser = localStorage.getItem('admin_user');
    return adminUser && JSON.parse(adminUser).role === 'admin';
  } catch {
    return false;
  }
}

/**
 * 獲取當前管理員用戶
 * @returns {Object|null} 管理員用戶資訊
 */
export function getCurrentAdminUser() {
  try {
    const adminUser = localStorage.getItem('admin_user');
    return adminUser ? JSON.parse(adminUser) : null;
  } catch {
    return null;
  }
}

/**
 * 登出管理員
 */
export function logoutAdmin() {
  localStorage.removeItem('admin_user');
}

// 導出初始化函數
export { initializeSupabase, ensureSupabaseInitialized };
