/**
 * Supabase 服務層
 * 封裝與 Supabase 的所有互動
 */

import { createClient } from '@supabase/supabase-js';

// 從環境變數獲取 Supabase 配置
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('Missing Supabase environment variables');
}

// 創建 Supabase 客戶端
export const supabase = createClient(supabaseUrl, supabaseAnonKey);

/**
 * 錯誤處理函數
 * @param {Object} error - Supabase 錯誤對象
 */
export function handleSupabaseError(error) {
  console.error('Supabase error:', error);
  throw new Error(error.message || 'Database operation failed');
}

/**
 * 檢查 Supabase 連接
 * @returns {Promise<{ok: boolean, error?: any}>}
 */
export async function checkSupabaseConnection() {
  try {
    const { error } = await supabase.from('projects').select('id').limit(1);
    if (error) {
      return { ok: false, error };
    }
    return { ok: true };
  } catch (err) {
    return { ok: false, error: err };
  }
}

/**
 * 獲取活躍的專案列表
 * @returns {Promise<Array>} 專案列表
 */
export async function fetchActiveProjects() {
  try {
    const { data: projects, error } = await supabase
      .from('projects')
      .select(`
        id,
        project_id_display,
        name,
        description,
        project_status,
        default_discount_id,
        created_at,
        updated_at,
        deadline,
        arrival_date,
        images,
        highlights,
        owner_id
      `)
      .eq('project_status', 'active')
      .order('created_at', { ascending: false });

    if (error) {
      handleSupabaseError(error);
    }

    if (!projects) return [];

    // 為每個專案獲取最低價格
    const projectsWithMinPrice = await Promise.all(
      projects.map(async project => {
        const { data: items, error: itemsError } = await supabase
          .from('items')
          .select('price')
          .eq('project_id', project.id)
          .eq('status', 'Available');

        if (itemsError) {
          console.error('Error fetching items for project', project.id, itemsError);
          return { ...project, minPrice: null };
        }

        const prices = (items || []).map(item => item.price);
        const minPrice = prices.length > 0 ? Math.min(...prices) : null;

        return { ...project, minPrice };
      })
    );

    return projectsWithMinPrice;
  } catch (error) {
    console.error('Error fetching active projects:', error);
    throw error;
  }
}

/**
 * 獲取指定專案的詳細資訊和商品
 * @param {string} projectId - 專案ID
 * @returns {Promise<{project: Object, items: Array}>}
 */
export async function fetchProjectWithItems(projectId) {
  try {
    // 獲取專案資訊
    const { data: project, error: projectError } = await supabase
      .from('projects')
      .select(`
        id,
        project_id_display,
        name,
        description,
        project_status,
        default_discount_id,
        created_at,
        updated_at,
        deadline,
        arrival_date,
        images,
        highlights,
        owner_id
      `)
      .eq('id', projectId)
      .single();

    if (projectError) {
      handleSupabaseError(projectError);
    }

    // 獲取專案商品
    const { data: items, error: itemsError } = await supabase
      .from('items')
      .select('*')
      .eq('project_id', projectId)
      .eq('status', 'Available')
      .order('sort_order', { ascending: true });

    if (itemsError) {
      handleSupabaseError(itemsError);
    }

    return { project, items: items || [] };
  } catch (error) {
    console.error('Error fetching project with items:', error);
    throw error;
  }
}

/**
 * 獲取指定專案的折扣規則
 * @param {string} projectId - 專案ID
 * @returns {Promise<Array>} 折扣規則列表
 */
export async function fetchProjectDiscounts(projectId) {
  try {
    const now = new Date().toISOString();

    const { data, error } = await supabase
      .from('discounts')
      .select(`
        *,
        discount_project_mappings!inner(project_id)
      `)
      .eq('discount_project_mappings.project_id', projectId)
      .eq('active', true)
      .or(`start_date.is.null,start_date.lte.${now}`)
      .or(`end_date.is.null,end_date.gte.${now}`);

    if (error) {
      handleSupabaseError(error);
    }

    return data || [];
  } catch (error) {
    console.error('Error fetching project discounts:', error);
    throw error;
  }
}

/**
 * 從 Supabase RPC 生成顯示ID
 * @param {string} prefix - ID前綴
 * @returns {Promise<string>} 生成的顯示ID
 */
export async function generateDisplayIdByRPC(prefix) {
  try {
    const { data, error } = await supabase.rpc('generate_display_id', { prefix });
    
    if (error || !data) {
      console.error('Error generating display ID:', error);
      throw error;
    }
    
    return data;
  } catch (error) {
    console.error('Error generating display ID:', error);
    throw error;
  }
}

/**
 * 創建訂單及訂單項目
 * @param {Object} params - 訂單參數
 * @returns {Promise<string>} 新訂單ID
 */
export async function createOrderWithItems(params) {
  try {
    const { userId, projectId, remarks, pickupDate, cartItems } = params;
    
    const { data, error } = await supabase.rpc('create_order_with_items', {
      p_user_id: userId,
      p_project_id: projectId,
      p_remarks: remarks,
      p_pickup_date: pickupDate,
      p_cart_items: cartItems,
    });
    
    if (error || !data) {
      console.error('Error creating order:', error);
      throw error;
    }
    
    return data; // 返回新訂單 UUID
  } catch (error) {
    console.error('Error creating order:', error);
    throw error;
  }
}

/**
 * 獲取用戶訂單列表
 * @param {string} userId - 用戶ID
 * @param {Object} filters - 篩選條件
 * @returns {Promise<Array>} 訂單列表
 */
export async function fetchUserOrders(userId, filters = {}) {
  try {
    let query = supabase
      .from('orders')
      .select(`
        id,
        user_id,
        order_number,
        status,
        total_amount,
        discount_amount,
        manual_discount_amount,
        final_amount,
        created_at,
        updated_at,
        pickup_date,
        notes,
        project_name
      `)
      .eq('user_id', userId);

    if (filters.orderNumber) {
      query = query.eq('order_number', filters.orderNumber);
    }

    if (filters.projectId) {
      query = query.eq('project_id', filters.projectId);
    }

    if (filters.status) {
      query = query.eq('status', filters.status);
    }

    const { data, error } = await query.order('created_at', { ascending: false });

    if (error) {
      handleSupabaseError(error);
    }

    return data || [];
  } catch (error) {
    console.error('Error fetching user orders:', error);
    throw error;
  }
}

/**
 * 用戶登入或註冊
 * @param {Object} userProfile - LIFF 用戶資料
 * @returns {Promise<Object>} 用戶資訊
 */
export async function upsertUser(userProfile) {
  try {
    const userData = {
      id: userProfile.userId,
      display_name: userProfile.displayName,
      picture_url: userProfile.pictureUrl,
      last_login_at: new Date().toISOString()
    };

    const { data, error } = await supabase
      .from('users')
      .upsert(userData, { onConflict: 'id' })
      .select()
      .single();

    if (error) {
      handleSupabaseError(error);
    }

    return data;
  } catch (error) {
    console.error('Error upserting user:', error);
    throw error;
  }
}
