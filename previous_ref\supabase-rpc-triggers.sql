-- 1. 顯示ID產生函數 (移除 discounts 相關邏輯)
create or replace function generate_display_id(prefix text)
returns text
language plpgsql
as $$
declare
  today text := to_char(current_date, 'YYYYMMDD');
  seq int;
  new_code text;
begin
  select max(substring(id_display from 10 for 4))::int into seq
  from (
    select case
      when prefix = 'P' then project_id_display
      when prefix = 'I' then item_id_display
      when prefix = 'O' then order_number
      else null end as id_display
    from (
      select project_id_display from projects
      union all select item_id_display from items
      union all select order_number from orders
    ) as all_ids
    where id_display like prefix || today || '-%'
  ) as seqs;

  seq := coalesce(seq, 0) + 1;
  new_code := prefix || today || '-' || lpad(seq::text, 4, '0');
  return new_code;
end;
$$;

-- 2. 計算最佳折扣函數
create or replace function calculate_order_discount(
  p_project_id uuid,
  p_cart_items jsonb
)
returns table(discount_id uuid, discount_amount numeric)
language plpgsql
as $$
declare
  discount_rec record;
  item jsonb;
  applicable_quantity int;
  applicable_subtotal numeric;
  current_discount numeric;
  max_discount numeric := 0;
  best_discount_id uuid := null;
begin
  for discount_rec in
    select d.*
    from discounts d
    join discount_project_mappings m on m.discount_id = d.id
    where m.project_id = p_project_id
      and d.active = true
      and (d.start_date is null or d.start_date <= now())
      and (d.end_date is null or d.end_date >= now())
      and d.quantity_threshold is not null
  loop
    applicable_quantity := 0;
    applicable_subtotal := 0;

    for item in select * from jsonb_array_elements(p_cart_items) loop
      if discount_rec.applicable_item_ids is null or array_length(discount_rec.applicable_item_ids,1) = 0
         or (item->>'itemId')::uuid = any(discount_rec.applicable_item_ids) then
        applicable_quantity := applicable_quantity + (item->>'quantity')::int;
        applicable_subtotal := applicable_subtotal + (item->>'subtotal')::numeric;
      end if;
    end loop;

    if applicable_quantity >= discount_rec.quantity_threshold then
      current_discount := applicable_quantity * discount_rec.discount_per_item;
      current_discount := least(current_discount, applicable_subtotal);
      if current_discount > max_discount then
        max_discount := current_discount;
        best_discount_id := discount_rec.id;
      end if;
    end if;
  end loop;

  discount_id := best_discount_id;
  discount_amount := max_discount;
  return next;
end;
$$;

-- 3. 訂單建立 RPC
create or replace function create_order_with_items(
  p_user_id uuid,
  p_project_id uuid,
  p_remarks text,
  p_pickup_date date,
  p_cart_items jsonb
)
returns uuid
language plpgsql
as $$
declare
  new_order_id uuid := gen_random_uuid();
  order_num text := generate_display_id('O');
  total numeric := 0;
  discount uuid;
  discount_amt numeric := 0;
  final_total numeric := 0;
  item jsonb;
begin
  -- 計算總金額
  for item in select * from jsonb_array_elements(p_cart_items)
  loop
    total := total + (item->>'subtotal')::numeric;
  end loop;

  -- 呼叫折扣計算函數
  select discount_id, discount_amount into discount, discount_amt
  from calculate_order_discount(p_project_id, p_cart_items);

  final_total := total - coalesce(discount_amt,0);

  insert into orders(id, order_number, user_id, project_id, total_amount, discount_amount, applied_discount_id, final_amount, remarks, pickup_date, status, order_date, created_at)
  values(new_order_id, order_num, p_user_id, p_project_id, total, coalesce(discount_amt,0), discount, final_total, p_remarks, p_pickup_date, 'Pending', now(), now());

  -- 插入訂單項目
  for item in select * from jsonb_array_elements(p_cart_items)
  loop
    insert into order_items(id, order_id, item_id, item_name, quantity, unit_price, subtotal, created_at)
    values(
      gen_random_uuid(),
      new_order_id,
      (item->>'itemId')::uuid,
      item->>'itemName',
      (item->>'quantity')::int,
      (item->>'unitPrice')::numeric,
      (item->>'subtotal')::numeric,
      now()
    );
  end loop;

  return new_order_id;
end;
$$;

-- 3.1 管理員手動調整訂單折扣 RPC
create or replace function update_order_manual_discount(
  p_order_id uuid,
  p_manual_discount_amount numeric,
  p_admin_id uuid,
  p_admin_notes text default null
)
returns table(final_amount numeric, manual_discount_amount numeric)
language plpgsql
as $$
declare
  total numeric;
  auto_discount numeric;
begin
  if p_manual_discount_amount < 0 then
    raise exception 'manual_discount_amount cannot be negative';
  end if;

  select total_amount, coalesce(discount_amount,0)
  into total, auto_discount
  from orders
  where id = p_order_id;

  if total is null then
    raise exception 'Order not found';
  end if;

  final_amount := total - auto_discount - p_manual_discount_amount;
  if final_amount < 0 then
    final_amount := 0;
  end if;

  update orders
  set manual_discount_amount = p_manual_discount_amount,
      manual_discount_applied_by = p_admin_id,
      manual_discount_applied_at = now(),
      admin_notes = p_admin_notes,
      final_amount = final_amount
  where id = p_order_id;

  manual_discount_amount := p_manual_discount_amount;
  return next;
end;
$$;

-- 4. 專案狀態自動更新函數
create or replace function update_project_status_func()
returns trigger
language plpgsql
as $$
begin
  if new.deadline is not null and now() > new.deadline and new.project_status = 'active' then
    new.project_status := 'ordering_ended';
  end if;
  if new.arrival_date is not null and now() > new.arrival_date and new.project_status in ('active', 'ordering_ended') then
    new.project_status := 'arrived';
  end if;
  return new;
end;
$$;

drop trigger if exists trg_update_project_status on projects;
create trigger trg_update_project_status
before update on projects
for each row
execute function update_project_status_func();

-- 5. 訂單狀態自動更新函數 (範例)
create or replace function update_order_status_func()
returns trigger
language plpgsql
as $$
begin
  -- 可根據付款、取貨狀態自動更新
  return new;
end;
$$;

drop trigger if exists trg_update_order_status on orders;
create trigger trg_update_order_status
before update on orders
for each row
execute function update_order_status_func();
