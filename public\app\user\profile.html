<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>小森活 - 用戶設定元件</title>
    <link rel="stylesheet" href="../shared-styles.css">
    <link rel="stylesheet" href="nav-icons.css">
    <style>
        /* 彈出視窗樣式 */
        .profile-popup {
            position: fixed;
            top: 60px;
            right: 10px;
            width: 240px;
            background-color: #ffffff;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
            z-index: 1000;
            overflow: hidden;
            animation: slideIn 0.2s ease;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            display: none; /* 預設隱藏 */
        }

        .profile-popup.active {
            display: block;
        }

        @keyframes slideIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .profile-header {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 16px;
            background-color: #f8f9fa;
            border-bottom: 1px solid #eaeaea;
        }

        .profile-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            overflow: hidden;
            background-color: #e9f5ff;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            color: #0078d7;
            font-weight: 600;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }

        .profile-avatar img { 
            width: 100%; 
            height: 100%; 
            object-fit: cover;
        }

        .profile-info h2 { 
            font-size: 16px; 
            margin: 0;
            color: #333;
            font-weight: 600;
        }

        .profile-content {
            padding: 16px;
        }

        .nickname-form {
            margin-bottom: 8px;
        }

        .form-label {
            display: block;
            margin-bottom: 6px;
            font-size: 13px;
            color: #555;
            font-weight: 500;
        }

        .form-input {
            width: 100%;
            padding: 10px 12px;
            border: 1px solid #ddd;
            border-radius: 8px;
            font-size: 14px;
            margin-bottom: 12px;
            transition: border-color 0.2s ease, box-shadow 0.2s ease;
        }

        .form-input:focus {
            border-color: #0078d7;
            box-shadow: 0 0 0 2px rgba(0, 120, 215, 0.2);
            outline: none;
        }

        .form-input::placeholder {
            color: #aaa;
        }

        .profile-actions {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .btn {
            width: 100%;
            padding: 10px 12px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            text-align: center;
            transition: all 0.2s ease;
        }

        .btn-primary {
            background-color: #0078d7;
            color: white;
        }

        .btn-primary:hover {
            background-color: #0069c0;
        }

        .btn-primary:active {
            transform: translateY(1px);
        }

        .btn-danger {
            background-color: #f44336;
            color: white;
            margin-top: 8px;
        }

        .btn-danger:hover {
            background-color: #d32f2f;
        }

        .btn-danger:active {
            transform: translateY(1px);
        }

        /* 覆蓋全局樣式 */
        * {
            box-sizing: border-box;
        }

        /* 示範用的頭像按鈕 */
        .demo-avatar-button {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            overflow: hidden;
            cursor: pointer;
            position: absolute;
            top: 10px;
            right: 10px;
            border: none;
            padding: 0;
            background: none;
        }
        
        .demo-avatar-button img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
    </style>
</head>
<body>
    <!-- 示範用的頭像按鈕 -->
    <button class="demo-avatar-button" id="avatarButton">
        <img src="https://picsum.photos/40/40?random=user" alt="用戶頭像">
    </button>

    <!-- 彈出式用戶設定視窗 -->
    <div class="profile-popup" id="profilePopup">
        <div class="profile-header">
            <div class="profile-avatar">
                <img src="https://picsum.photos/40/40?random=user" alt="用戶頭像" id="userAvatar">
            </div>
            <div class="profile-info">
                <h2 id="displayName">王小明</h2>
            </div>
        </div>

        <div class="profile-content">
            <form class="nickname-form" id="nicknameForm">
                <label for="communityNickname" class="form-label">社群暱稱</label>
                <input 
                    type="text" 
                    id="communityNickname" 
                    class="form-input" 
                    placeholder="設定您在小森活的暱稱" 
                    value="小明"
                    autocomplete="off"
                >
                <div class="profile-actions">
                    <button type="submit" class="btn btn-primary">儲存暱稱</button>
                    <button type="button" class="btn btn-danger" id="logoutBtn">登出</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        // 示範用的頭像按鈕點擊事件
        document.getElementById('avatarButton').addEventListener('click', function(e) {
            e.stopPropagation();
            const popup = document.getElementById('profilePopup');
            popup.classList.toggle('active');
        });

        // 表單提交事件
        document.getElementById('nicknameForm').addEventListener('submit', function(e) {
            e.preventDefault();
            const nickname = document.getElementById('communityNickname').value;
            if (!nickname.trim()) {
                alert('暱稱不能為空');
                return;
            }
            alert(`暱稱已更新為: ${nickname}`);
            // 實際應用中，這裡會發送API請求來更新暱稱
        });

        // 登出按鈕點擊事件
        document.getElementById('logoutBtn').addEventListener('click', function() {
            if (confirm('確定要登出嗎？')) {
                alert('您已登出');
                // 實際應用中，這裡會處理登出邏輯
                window.location.href = 'index.html';
            }
        });

        // 點擊視窗外部關閉視窗
        document.addEventListener('click', function(e) {
            const popup = document.getElementById('profilePopup');
            if (e.target.closest('#profilePopup') === null && e.target.closest('#avatarButton') === null) {
                popup.classList.remove('active');
            }
        });

        // 按ESC關閉視窗
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                document.getElementById('profilePopup').classList.remove('active');
            }
        });
    </script>
</body>
</html>